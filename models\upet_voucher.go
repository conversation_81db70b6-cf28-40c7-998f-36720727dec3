package models

type UpetVoucher struct {
	VoucherId         int    `json:"voucher_id" xorm:"not null pk autoincr comment('代金券编号') INT(11)"`
	VoucherCode       string `json:"voucher_code" xorm:"not null comment('代金券编码') VARCHAR(32)"`
	VoucherTId        int    `json:"voucher_t_id" xorm:"not null comment('代金券模版编号') index INT(11)"`
	VoucherTitle      string `json:"voucher_title" xorm:"not null comment('代金券标题') VARCHAR(50)"`
	VoucherDesc       string `json:"voucher_desc" xorm:"not null comment('代金券描述') VARCHAR(255)"`
	VoucherStartDate  int    `json:"voucher_start_date" xorm:"not null comment('代金券有效期开始时间') INT(11)"`
	VoucherEndDate    int    `json:"voucher_end_date" xorm:"not null comment('代金券有效期结束时间') INT(11)"`
	VoucherPrice      int    `json:"voucher_price" xorm:"not null comment('代金券面额') INT(11)"`
	VoucherLimit      string `json:"voucher_limit" xorm:"not null comment('代金券使用时的订单限额') DECIMAL(10,2)"`
	VoucherStoreId    int    `json:"voucher_store_id" xorm:"not null comment('代金券的店铺id') INT(11)"`
	VoucherState      int    `json:"voucher_state" xorm:"not null comment('代金券状态(1-未用,2-已用,3-过期,4-收回)') index(idx_owner_id_state) TINYINT(4)"`
	VoucherActiveDate int    `json:"voucher_active_date" xorm:"not null comment('代金券发放日期') INT(11)"`
	VoucherType       int    `json:"voucher_type" xorm:"default 0 comment('代金券类别') TINYINT(4)"`
	VoucherOwnerId    int    `json:"voucher_owner_id" xorm:"not null comment('代金券所有者id') index(idx_owner_id_state) INT(11)"`
	VoucherOwnerName  string `json:"voucher_owner_name" xorm:"not null comment('代金券所有者名称') VARCHAR(50)"`
	VoucherOrderId    int    `json:"voucher_order_id" xorm:"comment('使用该代金券的订单编号') INT(11)"`
	VoucherPwd        string `json:"voucher_pwd" xorm:"comment('代金券卡密不可逆') VARCHAR(100)"`
	VoucherPwd2       string `json:"voucher_pwd2" xorm:"comment('代金券卡密2可逆') VARCHAR(100)"`
	VoucherTType      int    `json:"voucher_t_type" xorm:"default 0 comment('适用范围') TINYINT(2)"`
	VoucherTGcId      string `json:"voucher_t_gc_id" xorm:"default '' comment('分类ID') VARCHAR(100)"`
	VoucherSpecialId  int    `json:"voucher_special_id" xorm:"default 0 comment('对应活动页面id') INT(11)"`
	VoucherBrandId    int    `json:"voucher_brand_id" xorm:"default 0 comment('品牌id') INT(11)"`
	VoucherVip        string `json:"voucher_vip" xorm:"default '' comment('是否会员卡使用') VARCHAR(20)"`
	VoucherFreeze     int    `json:"voucher_freeze" xorm:"default 0 comment('是否冻结 0否 1是') TINYINT(1)"`
	VoucherFrom       int    `json:"voucher_from" xorm:"default 0 comment('派券来源 0默认 1子龙办卡赠送,3新用户，4老用户，5支付成功赠送') TINYINT(1)"`
	WxCardCode        string `json:"wx_card_code" xorm:"comment('微信卡券code') VARCHAR(255)"`
	WxCouponCode      string `json:"wx_coupon_code" xorm:"comment('微信商家券code') VARCHAR(255)"`
	FromKey           string `json:"from_key" xorm:"comment('微页面领取key') VARCHAR(64)"`
}

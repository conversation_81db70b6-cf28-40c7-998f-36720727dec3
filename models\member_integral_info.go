package models

import (
	"time"
)

type MemberIntegralInfo struct {
	Id       int       `xorm:"not null pk autoincr comment('主键') INT(11)"`
	Memberid string    `xorm:"not null pk comment('用户id') CHAR(32)"`
	Integral int       `xorm:"not null comment('积分') INT(10)"`
	Lasttime time.Time `xorm:"not null default 'current_timestamp()' comment('最后操作时间') TIMESTAMP"`
	OrgId    int       `xorm:"comment('主体ID') INT(11)"`
}

type MemberIntegralInfoItem struct {
	Id       int    `xorm:"not null pk autoincr comment('主键') INT(11)"`
	Memberid string `xorm:"not null pk comment('用户id') CHAR(32)"`
	OrgId    int    `xorm:"comment('主体ID') INT(11)"`
}

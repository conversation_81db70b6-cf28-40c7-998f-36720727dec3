package dto

//北京扣减积分请求参数
type DecreaseIntegralRequest struct {
	//订单编号
	OrderNumber string `json:"orderNumber"`
	//医院编号
	ThirdOrgId string `json:"thirdOrgId"`
	//会员编号
	UserId string `json:"userId"`
	//单据类型 10：兑换扣减积分 11：退货扣减积分
	BillType int `json:"billType"`
	//商铺编码
	CompanyCode string `json:"companyCode"`
	//系统标识 0子龙 1电商 2ERP
	SystemId int `json:"systemId"`
	//订单金额BigDecimal
	Amount string `json:"amount"`
	//积分（空时候传0）
	Score string `json:"score"`
}

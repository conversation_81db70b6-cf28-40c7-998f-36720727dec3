package models

type IntegralExchangeAddress struct {
	Id            int32  `xorm:"not null pk autoincr comment('自增id') INT(11)"`
	ExchangeId    int32  `xorm:"not null comment('订单id') index INT(11)"`
	Name          string `xorm:"not null comment('收货人姓名') VARCHAR(50)"`
	AreaId        int32  `xorm:"not null comment('地区id') INT(11)"`
	AreaInfo      string `xorm:"not null comment('地区内容') VARCHAR(100)"`
	Address       string `xorm:"not null comment('详细地址') VARCHAR(200)"`
	Mobile        string `xorm:"default '''' comment('手机号码') VARCHAR(20)"`
	EncryptMobile string `xorm:"default '''' comment('加密手机号') VARCHAR(20)"`
	ShippingTime  int32  `xorm:"default NULL comment('配送时间') INT(11)"`
	ShippingCode  string `xorm:"default 'NULL' comment('物流单号') VARCHAR(50)"`
	ShippingEcode string `xorm:"default 'NULL' comment('物流公司编码') VARCHAR(30)"`
	ShippingEname string `xorm:"default 'NULL' comment('物流公司名称') VARCHAR(30)"`
}

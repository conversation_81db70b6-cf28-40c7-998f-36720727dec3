package models

import (
	"time"
)

type Order struct {
	OrderId          string `xorm:"not null pk default ''uuid()'' comment('订单id') VARCHAR(36)"`
	OldOrderSn       string `xorm:"not null default '''' comment('原电商父订单号') index VARCHAR(50)"`
	OrderSn          string `xorm:"not null default '''' comment('订单号') index VARCHAR(50)"`
	OrderStatus      int    `xorm:"not null default 0 comment('订单状态：0(已取消)10(默认):未付款;20:已付款;30:已完成') index index(order_status_2) INT(11)"`
	OrderStatusChild int    `xorm:"default NULL comment('子状态：
20101(美团默认)未接单;
20102已接单;
20103配送中;
20104已送达;
20105已取货;
20106已完成;
20107已取消;

10201(电商默认)未付款;
20201待发货;
20202全部发货;
20203确认收货;
20204部分发货;') INT(11)"`
	ShopId               string    `xorm:"not null default '''' comment('商户或门店id(财务编码)') VARCHAR(80)"`
	ShopName             string    `xorm:"not null default '''' comment('商户名称') VARCHAR(100)"`
	MemberId             string    `xorm:"not null default '''' comment('会员id') index VARCHAR(50)"`
	MemberName           string    `xorm:"not null default '''' comment('会员名称') VARCHAR(50)"`
	MemberTel            string    `xorm:"not null default '''' comment('会员手机号') VARCHAR(20)"`
	ReceiverName         string    `xorm:"not null default '''' comment('收件人') VARCHAR(50)"`
	ReceiverState        string    `xorm:"not null default '''' comment('收件省') VARCHAR(50)"`
	ReceiverCity         string    `xorm:"not null default '''' comment('收件市') VARCHAR(50)"`
	ReceiverDistrict     string    `xorm:"not null default '''' comment('收件区') VARCHAR(50)"`
	ReceiverAddress      string    `xorm:"not null default '''' comment('收件地址') VARCHAR(255)"`
	ReceiverPhone        string    `xorm:"not null default '''' comment('收件电话') VARCHAR(30)"`
	Privilege            int       `xorm:"not null default 0 comment('总优惠金额') INT(11)"`
	PayType              string    `xorm:"default '''' comment('Cod=货到付款, NoCod=非货到付款') VARCHAR(20)"`
	ReceiverMobile       string    `xorm:"not null default '''' comment('收件手机') VARCHAR(30)"`
	GjpStatus            string    `xorm:"not null default '''' comment('管家婆状态NoPay = 未付款 Payed = 已付款 Sended = 已发货 TradeSuccess = 交易成功 TradeClosed = 交易关闭 PartSend = 部分发货') VARCHAR(20)"`
	Total                int       `xorm:"not null default 0 comment('总金额（实际付款金额。加运费，加包装费，减优惠金额）') INT(11)"`
	GoodsTotal           int       `xorm:"not null default 0 comment('商品总金额（未加运费，未加包装费，服务费,减优惠金额）') INT(11)"`
	IsPay                int       `xorm:"not null default 0 comment('是否支付0否  1是') TINYINT(4)"`
	CreateTime           time.Time `xorm:"not null default 'current_timestamp()' comment('创建时间') index(order_status_2) DATETIME"`
	ConfirmTime          time.Time `xorm:"default 'NULL' comment('美团送达或电商确认收货时间(已完成)') DATETIME"`
	AcceptUsername       string    `xorm:"not null default '''' comment('接单人') VARCHAR(50)"`
	AcceptTime           time.Time `xorm:"default 'NULL' comment('美团接单时间') DATETIME"`
	IsPicking            int       `xorm:"default 0 comment('是否拣货1是 0否') TINYINT(4)"`
	PickingTime          time.Time `xorm:"default 'NULL' comment('美团拣货时间') DATETIME"`
	ExpectedTime         time.Time `xorm:"default 'NULL' comment('预计送达时间') DATETIME"`
	DeliverTime          time.Time `xorm:"default 'NULL' comment('美团配送或电商发货时间') DATETIME"`
	PayTime              time.Time `xorm:"default 'NULL' comment('支付时间') DATETIME"`
	PaySn                string    `xorm:"not null default '''' comment('支付单号') VARCHAR(50)"`
	OrderType            int       `xorm:"not null default 1 comment('订单类型1普通订单(默认),2预定订单,3门店自提(电商订单专用),4拼团订单,5门店配送') INT(11)"`
	Freight              int       `xorm:"not null default 0 comment('总运费') INT(11)"`
	Source               int       `xorm:"not null default 0 comment('仓库所属1:(a8 or 全渠道)  2:管易  3:门店（子龙）') TINYINT(4)"`
	Invoice              string    `xorm:"not null default '''' comment('发票信息') VARCHAR(255)"`
	WarehouseCode        string    `xorm:"not null default '''' comment('仓库代码') VARCHAR(255)"`
	BuyerMemo            string    `xorm:"not null default '''' comment('买家留言') VARCHAR(255)"`
	SellerMemo           string    `xorm:"not null default '''' comment('卖家留言') VARCHAR(255)"`
	GyDeliverStatus      int       `xorm:"not null default 0 comment('管易发货状态0未发货 1已发货 2部分发货') TINYINT(4)"`
	GyOrderSn            string    `xorm:"not null default '''' comment('管易订单号、第三方订单号') VARCHAR(50)"`
	OrderFrom            int       `xorm:"not null default 1 comment('1WEB2mobile3宠医云4ERP5智慧门店6有赞7阿闻宠物8阿闻商城 9美团') TINYINT(4)"`
	DeliveryType         int       `xorm:"not null default 1 comment('1快递 2外卖 3自提 4同城送') TINYINT(4)"`
	DeliveryRemark       string    `xorm:"not null default '''' comment('配送备注') VARCHAR(255)"`
	PushDelivery         int       `xorm:"default 0 comment('是否推送美团配送1是 0否') TINYINT(4)"`
	PushThirdOrder       int       `xorm:"default 0 comment('是否推送子龙或全渠道1是 0否') TINYINT(4)"`
	PushDeliveryReason   string    `xorm:"not null default '''' comment('推送美团配送失败原因') VARCHAR(255)"`
	PushThirdOrderReason string    `xorm:"not null default '''' comment('推送子龙或全渠道失败原因') VARCHAR(2000)"`
	Extras               string    `xorm:"not null default '''' comment('附加优惠信息') VARCHAR(500)"`
	PackingCost          int       `xorm:"not null default 0 comment('包装费') INT(11)"`
	ServiceCharge        int       `xorm:"not null default 0 comment('服务费') INT(11)"`
	CancelReason         string    `xorm:"not null default '''' comment('取消订单原因') VARCHAR(255)"`
	CancelTime           time.Time `xorm:"default 'NULL' comment('取消订单时间') DATETIME"`
	RemindTime           string    `xorm:"not null default '''' comment('催单时间戳（如用户发起了多次催单，此字段信息会推送多个催单时间）') VARCHAR(255)"`
	Latitude             float64   `xorm:"not null default 0.000000 comment('收货地址纬度') DOUBLE(10,6)"`
	Longitude            float64   `xorm:"not null default 0.000000 comment('收货地址经度') DOUBLE(10,6)"`
	PickupCode           string    `xorm:"not null default '''' comment('取货码') VARCHAR(30)"`
	TotalWeight          int       `xorm:"not null default 0 comment('总重量') INT(11)"`
	LogisticsCode        string    `xorm:"not null default '''' comment('如0000-商家自配、1001-美团加盟、2002-快送、3001-混合送（即美团专送+快送）等') VARCHAR(20)"`
	LockedStock          int       `xorm:"not null default 0 comment('是否锁定库存0否 1是') TINYINT(4)"`
}

package models

import (
	"time"
)

type MemberIntegralLock struct {
	Integralid string    `xorm:"not null comment('积分记录id(Erp过来的)') CHAR(50)"`
	Refundid   string    `xorm:"default '''' comment('退款流水号(中台生成的)') CHAR(50)"`
	Orderid    string    `xorm:"default '''' comment('订单号') CHAR(50)"`
	Lockid     string    `xorm:"default '''' comment('锁定id(billNumber)') CHAR(50)"`
	Createdate time.Time `xorm:"default 'NULL' comment('创建时间') DATETIME"`
}

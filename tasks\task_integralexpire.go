/*
	一年半以前积分过期处理
*/
package tasks

import (
	"context"
	"github.com/BurntSushi/toml"
	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	rp_kit "github.com/tricobbler/rp-kit"
	"integral-center/proto/igc"
	"integral-center/services"
)

type AppSetting struct {
	Server struct {
		ServerTag string //服务标记
	}
}

func init() {
	if rp_kit.EnvCanCron() {
		task := cron.New()
		//每1小时执行一次
		task.AddFunc("@every 60m", DealExpireIntegral)
		task.Start()

	}
	var AppSettings AppSetting
	toml.DecodeFile("appsetting.toml", &AppSettings)
	s := services.IntegralService{}

	//发MQ的机器不进行消费了，现在是发的速度跟不上
	//if AppSettings.Server.ServerTag != "1" {
	//处理所有用户积分MQ消费
	go s.DoAllUserDetail()
	//}
	// kfaf消费
	//go s.DoAllUserDetailKafka()

	//过期用户MQ消费
	go s.UserExpireDetail()
}

func DealExpireIntegral() {
	s := services.IntegralService{}
	go s.Expire()
}

func DoAllUser() {
	s := services.IntegralService{}
	go s.DoAllUser("1")
	//go s.DoAllUserKafka("1")
	//go s.DoAllUser("2")
	//go s.DoAllUser("3")
	//go s.DoAllUser("4")
}

type TaskChoose struct {
}

// 调用定时任务的grpc方法，方便测试使用
func (t *TaskChoose) ChooseTaskRun(ctx context.Context, in *igc.TaskRunVo) (*igc.BaseResponse, error) {
	response := igc.BaseResponse{
		Code:    200,
		Message: "",
	}
	map_data := make(map[int32]func(), 0)

	map_data[1] = DealExpireIntegral       // 积分过期
	map_data[2] = taskIntegralExpireNotice //积分过期提醒
	map_data[3] = DoAllUser                //跑所有用户数据重新计算

	glog.Info("手动启动定时任务：", in.Data)
	if fun, ok := map_data[in.Data]; ok {
		fun()
	}

	return &response, nil
}

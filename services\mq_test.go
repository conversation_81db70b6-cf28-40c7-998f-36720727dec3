package services

import (
	"testing"
)

func Test_sendIntegralChangeMsg(t *testing.T) {
	type args struct {
		memberId       string
		integraltype   int
		insertIntegral int
	}
	tests := []struct {
		name string
		args args
	}{
		{
			args: args{
				memberId:       "6601ae33cb7b4393bfb98ac92810777b",
				integraltype:   21,
				insertIntegral: 11,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sendIntegralChangeMsg(tt.args.memberId, tt.args.integraltype, tt.args.insertIntegral, 1)
		})
	}
}

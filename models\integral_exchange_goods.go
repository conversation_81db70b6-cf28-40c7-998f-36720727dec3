package models

type IntegralExchangeGoods struct {
	Id          int32   `xorm:"not null pk autoincr comment('订单礼品表索引') INT(11)"`
	ExchangeId  int32   `xorm:"not null comment('订单id') index INT(11)"`
	ProductId   int32   `xorm:"not null comment('兑换礼品id') index INT(11)"`
	GoodsId     string  `xorm:"not null comment('scrm_info，实物、虚拟skuid，优惠券id') VARCHAR(50)"`
	GoodsName   string  `xorm:"not null comment('礼品名称') VARCHAR(100)"`
	GoodsPrice  float32 `xorm:"not null default 0.00 comment('商品价格') DECIMAL(10,2)"`
	GoodsSerial string  `xorm:"not null default '''' comment('商品货号') VARCHAR(50)"`
	Integral    int32   `xorm:"not null comment('礼品兑换积分') INT(11)"`
	Qty         int32   `xorm:"not null comment('礼品数量') INT(11)"`
	GoodsImage  string  `xorm:"default 'NULL' comment('礼品图片') VARCHAR(512)"`
}

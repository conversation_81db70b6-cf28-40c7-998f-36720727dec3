package models

import (
	"time"
)

const (
	IETaskStateIng     = 0
	IETaskStateSuccess = 1
	IETaskStateFail    = 2
)

var IETaskStateMap = map[int32]string{
	IETaskStateIng:     "处理中",
	IETaskStateSuccess: "成功",
	IETaskStateFail:    "失败",
}

type IntegralExchangeTask struct {
	Id        int32     `xorm:"not null pk autoincr INT(10)"`
	UserNo    string    `xorm:"default '''' comment('操作人') VARCHAR(50)"`
	Type      int32     `xorm:"not null default 0 comment('任务类型 1批量导入物流、2批量导出订单') TINYINT(3)"`
	Req       string    `xorm:"default 'NULL' comment('请求参数') TEXT"`
	Result    string    `xorm:"not null default '''' comment('结果') VARCHAR(128)"`
	Url       string    `xorm:"not null default '''' comment('结果url') VARCHAR(128)"`
	State     int32     `xorm:"not null default 0 comment('状态0处理中，1成功，2失败') TINYINT(3)"`
	CreatedAt time.Time `xorm:"not null default 'current_timestamp()' comment('创建时间') DATETIME created"`
	UpdatedAt time.Time `xorm:"not null default 'current_timestamp()' comment('更新时间') DATETIME updated"`
}

package models

import (
	"errors"
	"integral-center/helpers"
	"integral-center/proto/igc"
	"time"

	"github.com/go-xorm/xorm"
	kit "github.com/tricobbler/rp-kit"
)

const (
	IETypeGoods      = 1 // 实物商品
	IETypeVr         = 2 // 虚拟商品
	IETypeCoupon     = 3 // 优惠券
	IETypeUPetCoupon = 4 // 商城优惠券
)

// 1第三方商品 2自有商品3门店卷 4商城券
const (
	IEGoodsTypeThirdProduct = 1 // 第三方商品
	IEGoodsOwnProduct       = 2 // 自有商品
	IEGoodsShop             = 3 // 门店卷
	IEGoodsMall             = 4 // 商城券
	IEGoodsCoin             = 5 // 爱心币
)

var IETypeMap = map[int32]string{
	IETypeGoods:  "实物商品",
	IETypeVr:     "虚拟商品",
	IETypeCoupon: "优惠券",
}

const (
	IEStatePay      = 10 // 待支付
	IEStateDelivery = 20 // 待发货
	IEStateUse      = 21 // 未使用
	IEStateTake     = 30 // 待收货
	IEStateFinished = 40 // 已完成
	IEStateUsed     = 41 // 已使用
	IEStateExpired  = 42 // 已过期
	IEStateCanceled = 50 // 已取消
)

var IEStateMap = map[int32]string{
	IEStatePay:      "待支付",
	IEStateDelivery: "待发货",
	IEStateUse:      "未使用",
	IEStateTake:     "待收货",
	IEStateFinished: "已完成",
	IEStateUsed:     "已使用",
	IEStateExpired:  "已过期",
	IEStateCanceled: "已取消",
}

const (
	IEFromDefault = 0
	IEFromPet     = 1
	IEFromMini    = 2
	IEFromApp     = 3
)

var IEFromMap = map[int32]string{
	IEFromDefault: "阿闻小程序",
	IEFromPet:     "宠医云",
	IEFromMini:    "阿闻小程序",
	IEFromApp:     "阿闻App",
}

type IntegralExchange struct {
	Id            int32  `xorm:"not null pk autoincr comment('兑换订单编号') INT(11)"`
	Type          int32  `xorm:"default 1 comment('订单类型 1实物2虚拟3优惠券') TINYINT(1)"`
	OrderSn       string `xorm:"not null comment('兑换订单编号') index VARCHAR(20)"`
	ErpOrderSn    string `xorm:"default '''' comment('erp订单号') VARCHAR(50)"`
	UserId        int32  `xorm:"not null comment('兑换会员id') INT(11)"`
	ScrmId        string `xorm:"not null default '''' comment('会员scrmId') VARCHAR(32)"`
	UserName      string `xorm:"not null comment('兑换会员姓名') VARCHAR(50)"`
	UserMobile    string `xorm:"not null default '''' comment('用户手机号') VARCHAR(11)"`
	EncryptMobile string `xorm:"not null default '''' comment('用户手机号') VARCHAR(20)"`
	FinnshedTime  int32  `xorm:"default NULL comment('订单完成时间') INT(11)"`
	IntegralTotal int32  `xorm:"not null default 0 comment('兑换总积分') INT(11)"`
	Remark        string `xorm:"default 'NULL' comment('订单留言') VARCHAR(300)"`
	State         int32  `xorm:"not null default 20 comment('订单状态 10待支付、20待发货、21未使用、30待收货、40已完成、41已使用、50已取消、51已过期') index TINYINT(4)"`
	From          int32  `xorm:"default 0 comment('订单来源0默认1宠医云2阿闻小程序 3阿闻app') TINYINT(1)"`
	ErpStatus     int32  `xorm:"default NULL comment('是否同步') TINYINT(4)"`
	ErpTime       int32  `xorm:"default NULL comment('ERP同步时间') INT(11)"`
	ErpOrderId    string `xorm:"default 'NULL' comment('ERP订单号') VARCHAR(50)"`
	ErpMobile     string `xorm:"default 'NULL' comment('手机号码') VARCHAR(11)"`
	VrOrderId     int32  `xorm:"default 0 comment('虚拟订单订单ID') INT(11)"`
	CouponCode    string `xorm:"default 'NULL' comment('优惠券码') VARCHAR(20)"`
	CouponEndTime int32  `xorm:"default 0 comment('优惠券过期时间') INT(11)"`
	Gtype         int32  `xorm:"default 0 comment('商品类型 1第三方商品 2自有商品') TINYINT(1)"`
	AddTime       int32  `xorm:"not null comment('兑换订单生成时间') INT(11)"`
	DeleteTime    int32  `xorm:"not null comment('删除时间') INT(11)"`
}

type IntegralExchangeExtend struct {
	*IntegralExchange        `xorm:"extends"`
	*IntegralExchangeAddress `xorm:"extends"`
	// 商品是一对多的关系
	IntegralExchangeGoods []*IntegralExchangeGoods
}

// OrderDetail 订单详情结构体
type OrderDetail struct {
	// 订单信息
	Order *igc.OrderListData
	// 兑换信息，仅当type=3有效
	Coupon *igc.OrderCouponData
	// 核销码，仅当type=2有效
	VerifyCode *igc.OrderVerifyCodeData
	// 步骤数据
	Steps []*igc.OrderStepData
}

// GetStateText 获取状态文本描述
func (ie IntegralExchange) GetStateText() string {
	return IEStateMap[ie.State]
}

// GetFromText 获取来源文本描述
func (ie IntegralExchange) GetFromText() string {
	return IEFromMap[ie.From]
}

// GetTypeText 获取类型文本描述
func (ie IntegralExchange) GetTypeText() string {
	return IETypeMap[ie.Type]
}

// QueryIntegralExchangeExtendByReq 列表查询订单数据
func QueryIntegralExchangeExtendByReq(in *igc.OrderListRequest) (query *xorm.Session, err error) {
	db := GetDBConn()
	query = db.Table("dc_order.integral_exchange").Alias("e").
		Join("left", "dc_order.integral_exchange_address a", "a.exchange_id = e.id").
		Where("e.delete_time = 0").OrderBy("e.id desc")

	if len(in.Channel) > 0 {
		query.Where("e.from = ?", in.Channel)
	}
	if len(in.StartDate) > 0 {
		if t, err := time.ParseInLocation(kit.DATE_LAYOUT, in.StartDate, time.Local); err != nil {
			return nil, errors.New("起始日期格式错误 " + err.Error())
		} else {
			query.Where("e.add_time >= ?", t.Unix())
		}
	}
	if len(in.EndDate) > 0 {
		if t, err := time.ParseInLocation(kit.DATETIME_LAYOUT, in.EndDate+" 23:59:59", time.Local); err != nil {
			return nil, errors.New("结束日期格式错误 " + err.Error())
		} else {
			query.Where("e.add_time <= ?", t.Unix())
		}
	}
	// 商品一对多
	if len(in.GoodsName) > 0 {
		query.And("e.id in (select exchange_id from dc_order.integral_exchange_goods eg where eg.goods_name like ?)", "%"+in.GoodsName+"%")
	}
	if len(in.Mobile) > 0 {
		query.Where("e.user_mobile = ?", in.Mobile)
	}
	if len(in.OrderSn) > 0 {
		query.Where("e.order_sn = ?", in.OrderSn)
	}
	if len(in.State) > 0 {
		query.Where("e.state = ?", in.State)
	}
	if len(in.Type) > 0 {
		query.Where("e.type = ?", in.Type)
	}
	return
}

// PreloadExtendGoods 预加载商品信息
func PreloadExtendGoods(extends []*IntegralExchangeExtend) error {
	var ids []int32
	extendsMap := make(map[int32]*IntegralExchangeExtend)
	for _, extend := range extends {
		ids = append(ids, extend.IntegralExchange.Id)
		extendsMap[extend.IntegralExchange.Id] = extend
	}
	if len(ids) < 1 {
		return nil
	}

	var exchangeGoods []*IntegralExchangeGoods
	db := GetDBConn()
	if err := db.Table("dc_order.integral_exchange_goods").In("exchange_id", ids).Find(&exchangeGoods); err != nil {
		return errors.New("预加载商品出错 " + err.Error())
	}

	for _, eg := range exchangeGoods {
		extendsMap[eg.ExchangeId].IntegralExchangeGoods = append(extendsMap[eg.ExchangeId].IntegralExchangeGoods, eg)
	}
	return nil
}

// ToOrderList 查询结构转订单列表数据
func (ie IntegralExchangeExtend) ToOrderList() (data *igc.OrderListData) {
	data = &igc.OrderListData{
		OrderSn:       ie.OrderSn,
		AddTime:       helpers.TimestampToDateTime(ie.AddTime),
		From:          ie.GetFromText(),
		MemberName:    ie.UserName,
		MemberMobile:  ie.UserMobile,
		EncryptMobile: ie.IntegralExchange.EncryptMobile,
		ScrmId:        ie.ScrmId,
		Remark:        ie.Remark,
		State:         ie.State,
		StateText:     ie.GetStateText(),
		Type:          ie.Type,
		GoodType:      ie.Gtype,
		Total:         ie.IntegralTotal,
		FinnshedTime:  helpers.TimestampToDateTime(ie.FinnshedTime),
	}

	for _, eg := range ie.IntegralExchangeGoods {
		data.Goods = append(data.Goods, &igc.GoodsListData{
			ProductId: eg.ProductId,
			Id:        eg.GoodsId,
			Name:      eg.GoodsName,
			Price:     eg.GoodsPrice,
			Serial:    eg.GoodsSerial,
			Integral:  eg.Integral,
			Qty:       eg.Qty,
			Image:     helpers.GetIntegralProductUrl(eg.GoodsImage, true),
			Total:     eg.Integral * eg.Qty,
		})
	}

	// 实物订单填充地址信息
	if ie.Type == 1 {
		data.Address = &igc.OrderAddressData{
			Name:          ie.Name,
			Mobile:        ie.Mobile,
			EncryptMobile: ie.IntegralExchangeAddress.EncryptMobile,
			AreaInfo:      ie.AreaInfo,
			Address:       ie.Address,
			ShippingCode:  ie.ShippingCode,
			ShippingTime:  helpers.TimestampToDateTime(ie.ShippingTime),
			ShippingEcode: ie.ShippingEcode,
			ShippingEname: ie.ShippingEname,
		}
	}
	return
}

// ToOrderDetail 查询结构转订单详情数据
func (ie IntegralExchangeExtend) ToOrderDetail() (detail *OrderDetail, err error) {
	detail = &OrderDetail{Order: ie.ToOrderList()}

	// 订单脚印
	detail.Steps = append(detail.Steps, &igc.OrderStepData{
		Time: helpers.TimestampToDateTime(ie.AddTime),
		Desc: "下单",
	}, &igc.OrderStepData{
		Time: helpers.TimestampToDateTime(ie.AddTime),
		Desc: "支付成功",
	})

	switch ie.Type {
	case IETypeGoods: // 实物订单
		if ie.ShippingTime > 0 {
			detail.Steps = append(detail.Steps, &igc.OrderStepData{
				Time: helpers.TimestampToDateTime(ie.ShippingTime),
				Desc: "已发货",
			})
		}
		if ie.FinnshedTime > 0 {
			detail.Steps = append(detail.Steps, &igc.OrderStepData{
				Time: helpers.TimestampToDateTime(ie.FinnshedTime),
				Desc: "已完成",
			})
		}
	case IETypeVr: // 积分兑换虚拟商品
		upetDb := GetUpetDBConn()
		detail.VerifyCode = new(igc.OrderVerifyCodeData)
		if has, err := upetDb.SQL("select FROM_UNIXTIME(vr_indate) as end_time,FROM_UNIXTIME(vr_indate,'%Y-%m-%d') as end_date,"+
			"vr_code as code,vr_state as state,FROM_UNIXTIME(vr_usetime) as use_time,chain_name "+
			"from upet_vr_order_code where order_id = ?", ie.VrOrderId).
			Get(detail.VerifyCode); err != nil {
			return detail, errors.New("反查商城虚拟订单信息出错 " + err.Error())
		} else if has {
			if detail.VerifyCode.UseTime != "" {
				detail.Steps = append(detail.Steps, &igc.OrderStepData{
					Time: detail.VerifyCode.UseTime,
					Desc: "核销成功",
				})
			} else if ie.State == IEStateExpired {
				detail.Steps = append(detail.Steps, &igc.OrderStepData{
					Time: helpers.TimestampToDateTime(ie.CouponEndTime),
					Desc: "兑换码过期",
				})
			}
		}
	case IETypeCoupon: // 积分兑换优惠券
		detail.Coupon = &igc.OrderCouponData{
			EndTime: helpers.TimestampToDateTime(ie.CouponEndTime),
			EndDate: helpers.TimestampToDate(ie.CouponEndTime),
			Code:    ie.CouponCode,
		}
		// 兑换成功即完成
		if ie.FinnshedTime > 0 {
			detail.Steps = append(detail.Steps, &igc.OrderStepData{
				Time: helpers.TimestampToDateTime(ie.FinnshedTime),
				Desc: "已完成",
			})
		} else if ie.State == IEStateExpired {
			detail.Steps = append(detail.Steps, &igc.OrderStepData{
				Time: helpers.TimestampToDateTime(ie.CouponEndTime),
				Desc: "已过期",
			})
		}
	}

	return detail, nil
}

// Delivery 发货或者更新物流
func (ie IntegralExchangeExtend) Delivery(session *xorm.Session, address *IntegralExchangeAddress) error {
	if ie.State != IEStateTake {
		if _, err := session.Table("dc_order.integral_exchange").Where("id = ?", ie.IntegralExchange.Id).Update(map[string]interface{}{
			"state": IEStateTake,
		}); err != nil {
			return errors.New("更新发货状态出错 " + err.Error())
		}
	}

	if _, err := session.Table("dc_order.integral_exchange_address").Where("id = ?", ie.IntegralExchangeAddress.Id).Update(address); err != nil {
		return errors.New("更新发货信息出错 " + err.Error())
	}

	return nil
}

package services

import (
	"context"
	"integral-center/helpers"
	"integral-center/helpers/order"
	"integral-center/models"
	"integral-center/pkg/http/zilong"
	"integral-center/proto/igc"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

type GoodsService struct {
}

// List 后台积分商品列表
func (g *GoodsService) List(ctx context.Context, in *igc.GoodsListRequest) (out *igc.GoodsListResponse, e error) {
	out = &igc.GoodsListResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("积分商城 GoodsList 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	db := models.GetDBConn()
	query := db.Table("dc_product.integral_product")

	if in.Show == "0" || in.Show == "1" {
		query.Where("`show`=?", in.Show)
	}
	if in.Commend == "0" || in.Commend == "1" {
		query.Where("`commend`=?", in.Commend)
	}

	if in.Type == "1" || in.Type == "2" || in.Type == "3" {
		query.Where("`type`=?", in.Type)
	}
	if len(in.Name) > 0 {
		query.Where("`name` like ?", "%"+in.Name+"%")
	}
	// 只获取爱心币
	if in.GoodType > 0 {
		query.Where("`good_type`=?", in.GoodType)
		query.OrderBy("create_time desc ")
	} else {
		query.OrderBy("id desc")
	}

	pr := &helpers.PaginateReq{
		Count:    query.Clone().Select("count(*)"),
		List:     query.OrderBy("id desc"),
		Page:     in.PageIndex,
		PageSize: in.PageSize,
	}

	var productList []*models.IntegralProduct
	if err := pr.Paginate(&out.Total, &productList); err != nil {
		out.Message = err.Error()
		return
	}

	for i := 0; i < len(productList); i++ {
		out.Data = append(out.Data, &igc.GoodsList{
			Id:      productList[i].Id,
			Name:    productList[i].Name,
			Type:    productList[i].Type,
			Points:  productList[i].Points,
			Storage: productList[i].Storage,
			Show:    productList[i].Show,
			Commend: productList[i].Commend,
			SaleNum: productList[i].SaleNum,
			AddTime: productList[i].CreateTime.Format(kit.DATETIME_LAYOUT),
		})
	}

	out.Code = 200
	return
}

// Detail 后台积分商品详情
func (g *GoodsService) Detail(ctx context.Context, in *igc.GoodsIdRequest) (out *igc.GoodsDetailResponse, e error) {
	out = &igc.GoodsDetailResponse{Code: 400, Data: &igc.GoodsDetail{}}

	defer func() {
		if out.Code == 400 {
			glog.Info("积分商城 GoodsDetail 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	db := models.GetDBConn()
	//sql := "select `id`,`name`,`price`,`points`,`image`,`serial`,`storage`,`show`,`commend`,`body`,`is_limit`,`limit_num`,`is_limit_time`,`limit_mgrade`,`start_time`,`end_time`,`type`,`scrm_info`,`good_type` from dc_product.integral_product where id = ?"

	product := &models.IntegralProduct{}
	ok, err := db.Table("dc_product.integral_product").Where("id=?", in.Id).Get(product)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		return
	}
	if !ok {
		out.Code = 400
		out.Message = "商品不存在"
		return
	}
	out.Data = product.ToIgcGoodsDetail()
	out.Code = 200
	return
}

// Store 后台积分商品保存
func (g *GoodsService) Store(ctx context.Context, in *igc.GoodsDetail) (out *igc.GoodsResponse, e error) {
	out = &igc.GoodsResponse{Code: 400}
	defer func() {
		if out.Code == 400 {
			glog.Info("积分商城 GoodsStore 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if in.Id < 1 && (in.Type < 1 || in.Type > 3) {
		out.Message = "商品类型错误"
		return
	}

	in.Name = strings.Trim(in.Name, " ")
	if in.Name == "" {
		out.Message = "商品名称必填"
		return
	}

	if utf8.RuneCountInString(in.Name) > 30 {
		out.Message = "商品名称超出30个字"
		return
	}

	maxInt64 := int64(999999999999999)
	maxInt32 := int32(999999999)

	if in.Type == 1 {
		if in.ScrmInfo < 1 {
			out.Message = "skuid必填"
			return
		}
		if in.ScrmInfo > maxInt64 {
			out.Message = "skuid错误，超出最大值"
			return
		}
		if _, err := models.GetValidGoods(int32(in.ScrmInfo), in.OrgId); err != nil {
			out.Message = "兑换" + err.Error()
			return
		}
	}

	if in.Type == 2 {
		if in.ScrmInfo > 0 {
			if _, err := models.GetValidVirtualGoods(int32(in.ScrmInfo), in.OrgId); err != nil {
				out.Message = "兑换" + err.Error()
				return
			}
		}
	}

	if in.Type == 3 {
		if in.ScrmInfo < 1 {
			out.Message = "券id必填"
			return
		}
		if in.ScrmInfo > maxInt64 {
			out.Message = "券id错误，超出最大值"
			return
		}
		//门店券验证
		if in.GoodType == models.IEGoodsShop {
			if _, template, err := zilong.CouponTemplateDetail(int32(in.ScrmInfo)); err != nil {
				out.Message = err.Error()
				return
			} else if template.StatusValue > 40 || template.StatusValue < 30 {
				out.Message = "门店券状态" + template.Status + "不允许提交"
				return
			} else if template.Inventory < 1 {
				out.Message = "门店券库存不足"
				return
			}
		} else if in.GoodType == models.IEGoodsMall { //商城券验证
			upetDb := models.GetUpetDBConn()
			strScrmInfo := cast.ToString(in.ScrmInfo)
			err := order.ValidateMallCoupon(upetDb, strScrmInfo, in.Storage)
			if err != nil {
				out.Message = err.Error()
				return
			}
		}
	}

	if in.Type != 2 {
		if in.Price < 0.01 || in.Price > float32(maxInt32) {
			out.Message = "市场价错误"
			return
		}
	}

	if in.Points < 1 || in.Points > maxInt32 {
		out.Message = "兑换价（积分）错误"
		return
	}

	if in.Storage < 0 || in.Storage > maxInt32 {
		out.Message = "兑换库存错误"
		return
	}

	if in.Image == "" {
		out.Message = "商品头图必传"
		return
	}

	if in.IsLimit == 1 {
		if in.LimitNum < 1 {
			out.Message = "每人最多兑换数量必填"
			return
		} else if in.LimitNum > maxInt32 {
			out.Message = "每人最多兑换数量错误，超出最大值"
			return
		}
	}

	if in.Body == "" {
		out.Message = "商品描述必填"
		return
	}

	var model models.IntegralProduct

	// 限制会员兑换，改成vip等级兑换
	var userLeverIds []string

	if len(in.UserLevelIds) > 0 {
		in.LimitMgrade = 1 // 限制参与兑换的会员级别
		for i := range in.UserLevelIds {
			ids := cast.ToString(in.UserLevelIds[i])
			userLeverIds = append(userLeverIds, cast.ToString(ids))
		}
	}

	model.Name = in.Name
	model.Price = cast.ToString(in.Price)
	model.Points = in.Points
	model.Image = in.Image
	model.Serial = in.Serial
	model.Storage = in.Storage
	model.Show = in.Show
	model.Commend = in.Commend
	model.Body = in.Body
	model.IsLimit = in.IsLimit
	model.LimitNum = in.LimitNum
	model.IsLimitTime = in.IsLimitTime
	model.LimitMgrade = in.LimitMgrade
	if in.ScrmInfo > 0 {
		model.ScrmInfo = cast.ToString(in.ScrmInfo)
	} else {
		model.ScrmInfo = ""
	}
	model.ScrmInfoState = 1 //默认上架
	if in.LimitMgrade == 0 {
		model.UserLevelIds = ""
	} else {
		model.UserLevelIds = strings.Join(userLeverIds, ",")
	}

	if in.Id == 0 {
		//编辑的时候不允许修改这两个字段
		model.Type = in.Type
		model.GoodType = in.GoodType
	}
	// 爱心币数量
	model.CoinNum = in.CoinNum

	if in.IsLimitTime == 1 {
		if in.StartTime == "" {
			out.Message = "开始时间必填"
			return
		}

		if in.EndTime == "" {
			out.Message = "结束时间必填"
			return
		}

		if startTime, err := time.ParseInLocation(kit.DATETIME_LAYOUT, in.StartTime, time.Local); err != nil {
			out.Message = "开始时间格式错误"
			return
		} else {
			model.StartTime = startTime
		}

		if endTime, err := time.ParseInLocation(kit.DATETIME_LAYOUT, in.EndTime, time.Local); err != nil {
			out.Message = "结束时间格式错误"
			return
		} else {
			model.EndTime = endTime
		}
		if model.StartTime.After(model.EndTime) {
			out.Message = "开始时间不能大于结束时间"
			return
		}
	}

	db := models.GetDBConn()
	query := db.Table("dc_product.integral_product")

	mustCols := []string{
		"show",
		"commend",
		"limit_mgrade",
		"is_limit_time",
		"is_limit",
		"limit_num",
		"storage",
		"user_level_ids",
		"coin_num",
	}
	if in.Id > 0 {
		if _, err := query.ID(in.Id).MustCols(mustCols...).Update(model); err != nil {
			out.Message = err.Error()
			return
		}
	} else {
		if _, err := query.MustCols(mustCols...).Insert(model); err != nil {
			out.Message = err.Error()
			return
		}
	}

	out.Code = 200
	return
}

// Patch 后台积分商品更新
func (g *GoodsService) Patch(ctx context.Context, in *igc.GoodsPatchRequest) (out *igc.GoodsResponse, e error) {
	out = &igc.GoodsResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("积分商城 GoodsPatch 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if in.Id < 1 {
		out.Message = "参数错误"
		return
	}

	var model models.IntegralProduct
	mustCols := make([]string, 0)
	if in.Show != nil && (in.Show.Value == 0 || in.Show.Value == 1) {
		mustCols = append(mustCols, "show")
		model.Show = in.Show.Value
	}
	if in.Commend != nil && (in.Commend.Value == 0 || in.Commend.Value == 1) {
		mustCols = append(mustCols, "commend")
		model.Commend = in.Commend.Value
	}

	if len(mustCols) == 0 {
		out.Message = "参数错误"
		return
	}

	db := models.GetDBConn()
	query := db.Table("dc_product.integral_product")

	if _, err := query.ID(in.Id).MustCols(mustCols...).Update(model); err != nil {
		out.Message = err.Error()
		return
	}

	out.Code = 200
	return
}

// AWList 小程序积分商品列表
func (g *GoodsService) AWList(ctx context.Context, in *igc.AWGoodsListRequest) (out *igc.AWGoodsListResponse, e error) {
	out = &igc.AWGoodsListResponse{Code: 400}
	defer func() {
		if out.Code == 400 {
			glog.Info("积分商城 GoodsAWList 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	db := models.GetDBConn()
	query := db.Table("dc_product.integral_product")

	query.Where("`show`=1 and storage>0 and scrm_info_state=1 and (is_limit_time=0 or (is_limit_time=1 and end_time>now()))")

	if in.GoodType > 0 {
		query.OrderBy("create_time desc")
		query.Where("good_type = ? ", in.GoodType)
	} else {
		if in.Sort == "0" { //倒排
			query.OrderBy("id desc")
		}
		if in.Sort == "1" { //积分从低到高
			query.OrderBy("points asc,id desc")
		}
		if in.Sort == "2" { //积分从高到低
			query.OrderBy("points desc,id desc")
		}

		if in.Commend == "0" || in.Commend == "1" {
			query.Where("commend=?", in.Commend)
		}
	}

	if in.PointsStart != "" && cast.ToInt32(in.PointsStart) > 0 {
		query.Where("points>=?", in.PointsStart)
	}
	if in.PointsEnd != "" && cast.ToInt32(in.PointsEnd) > 0 {
		query.Where("points<=?", in.PointsEnd)
	}
	if in.ShowType == "1" {
		integral := int32(0)
		_, _ = db.Table("member_integral_info").Where("memberid=?", in.ScrmId).Cols("integral").Get(&integral)
		query.Where("points<=?", integral)
	}
	if len(in.Keyword) > 0 {
		query.Where("name like ?", "%"+in.Keyword+"%")
	}

	//这里去掉了body, scrm_info两个字段的查询返回
	pr := &helpers.PaginateReq{
		Count:    query.Clone().Select("count(*)"),
		List:     query.Select("`id`,`name`,`price`,`points`,`image`,`serial`,`storage`,`show`,`commend`,`is_limit`,`limit_num`,`is_limit_time`,`limit_mgrade`,`start_time`,`end_time`,`type`,`good_type`,`sale_num`"),
		Page:     in.PageIndex,
		PageSize: in.PageSize,
	}
	if err := pr.Paginate(&out.Total, &out.Data); err != nil {
		out.Message = err.Error()
	} else {
		for k, v := range out.Data {
			out.Data[k].Image = helpers.GetIntegralProductUrl(v.Image, true)
		}
		out.Code = 200
	}
	return
}

// AWDetail 小程序积分商品详情
func (g *GoodsService) AWDetail(ctx context.Context, request *igc.GoodsIdRequest) (out *igc.AWGoodsDetailResponse, e error) {
	out = &igc.AWGoodsDetailResponse{Code: 400, Data: &igc.GoodsDetail{}, Extra: &igc.AWGoodsDetailResponse_Extra{}}

	defer func() {
		if out.Code == 400 {
			glog.Info("积分商城 GoodsAWDetail 入参：", kit.JsonEncode(request), "，返回：", kit.JsonEncode(out))
		}
	}()

	db := models.GetDBConn()
	//sql := "select `id`,`name`,`price`,`points`,`image`,`serial`,`storage`,`show`,`commend`,`body`,`is_limit`,`limit_num`,`is_limit_time`,`limit_mgrade`,`start_time`,`end_time`,`type`,`scrm_info`,`good_type`,`sale_num` from dc_product.integral_product where id = ?"
	product := &models.IntegralProduct{}
	if ok, err := db.Table("dc_product.integral_product").Where("id=?", request.Id).Get(product); err != nil {
		out.Message = err.Error()
		return
	} else if !ok {
		out.Message = "商品不存在"
		return
	} else if product.Show != 1 {
		out.Message = "商品已下架"
		return
	}
	out.Data = product.ToIgcGoodsDetail()
	// 积分查询
	var integral int32
	if _, err := db.Table("member_integral_info").Where("memberid=?", request.ScrmId).Cols("integral").Get(&integral); err != nil {
		out.Message = "查询用户积分出错 " + err.Error()
		return
	}
	if integral >= out.Data.Points {
		out.Extra.IntegralState = 1 // 代表用户积分够兑换这个商品
	}
	if out.Data.LimitMgrade > 0 {
		// 会员限制查询
		if has, err := db.Table("member_card_relation").Where("userid=? and vipstatus=1 and cardtype=2", request.ScrmId).Exist(); err != nil {
			out.Message = "查询会员卡出错 " + err.Error()
			return
		} else if has {
			out.Extra.MemberVipState = 1
		}
	}
	if out.Data.IsLimitTime == 1 {
		startTime, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, out.Data.StartTime, time.Local)
		endTime, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, out.Data.EndTime, time.Local)
		nowTime := time.Now()
		if nowTime.Before(startTime) {
			out.Extra.GoodsState = 1 //即将开始
		} else if nowTime.After(startTime) && nowTime.Before(endTime) {
			out.Extra.GoodsState = 2 //进行中
			if out.Data.Storage < 1 {
				out.Extra.GoodsState = 3 //库存不足
			}
		} else if nowTime.After(endTime) {
			out.Extra.GoodsState = 0 //结束
		}
	} else {
		if out.Data.Storage > 0 {
			out.Extra.GoodsState = 2 //进行中
		} else {
			out.Extra.GoodsState = 3 //库存不足
		}
	}

	if out.Data.Type == 2 && out.Data.ScrmInfo > 0 {
		g := new(models.UpetGoods)
		if has, err := models.GetUpetDBConn().Table("upet_goods").Where("goods_id = ? AND store_id=?", out.Data.ScrmInfo, out.Data.OrgId).Get(g); err != nil {
			out.Message = "兑换商品查询出错"
			return
		} else if !has {
			out.Message = "兑换商品不存在"
			return
		} else if g.GoodsState != 1 { //下架
			out.Message = "兑换商品已下架"
			//out.Extra.GoodsState = 0 //结束
			return
		} else if g.ValidityType == 1 && g.VirtualIndate < int32(time.Now().Unix()) {
			out.Message = "兑换商品已结束"
			//out.Extra.GoodsState = 0 //结束 指定时间有效
			return
		}
	}

	if out.Data.Type == models.IETypeCoupon {
		if out.Data.GoodType == models.IEGoodsShop {
			if _, template, err := zilong.CouponTemplateDetail(int32(out.Data.ScrmInfo)); err != nil {
				out.Message = err.Error()
				return
			} else if template.StatusValue > 40 || template.StatusValue < 30 {
				out.Message = "门店券" + template.Status
				//if template.StatusValue < 40 {
				//	out.Extra.GoodsState = 1 //即将开始
				//} else {
				//	out.Extra.GoodsState = 0 //结束
				//}
				return
			} else if template.Inventory < 1 {
				//out.Extra.GoodsState = 3 //库存不足
				out.Message = "门店券库存不足"
				return
			}
		} else if out.Data.GoodType == models.IEGoodsMall {
			UpetDb := models.GetUpetDBConn()
			strScrmInfo := cast.ToString(out.Data.ScrmInfo)
			err := order.ValidateMallCoupon(UpetDb, strScrmInfo, out.Data.Storage)
			if err != nil {
				out.Message = err.Error()
				return
			}
		}
	}

	out.Code = 200
	return
}

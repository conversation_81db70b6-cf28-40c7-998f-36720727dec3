package order

import (
	"github.com/go-xorm/xorm"
	"github.com/spf13/cast"
	"integral-center/models"
	"integral-center/proto/igc"
	"testing"
)

func Test_makeOrderSn(t *testing.T) {
	type args struct {
		memberId int32
	}
	tests := []struct {
		name string
		args args
		want string
	}{

		{
			args: args{memberId: cast.ToInt32(12344345666)},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := makeOrderSn(tt.args.memberId); got != tt.want {
				t.Errorf("makeOrderSn() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStore(t *testing.T) {
	type args struct {
		in *igc.AWOrderStoreRequest
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			args: args{in: &igc.AWOrderStoreRequest{
				GoodsId:   64,
				AddressId: 8347,
				ScrmId:    "02e2c06214aa4b7497199922af0e187c",
			}},
		},
		/*{
			args: args{in: &igc.AWOrderStoreRequest{
				GoodsId: 86,
				//AddressId: 8347,
				ScrmId: "02e2c06214aa4b7497199922af0e187c",
			}},
		},*/
		/*{
			args: args{in: &igc.AWOrderStoreRequest{
				GoodsId:   70,
				AddressId: 8347,
				ScrmId:    "02e2c06214aa4b7497199922af0e187c",
			}},
		},*/
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := Store(tt.args.in); (err != nil) != tt.wantErr {
				t.Errorf("Store() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_storeHandler_validateMallCoupon(t *testing.T) {
	type fields struct {
		Product                 *models.IntegralProduct
		UpetGoods               *models.UpetGoods
		Address                 *models.UpetAddress
		Member                  *models.UpetMember
		IntegralExchange        *models.IntegralExchange
		IntegralExchangeGoods   *models.IntegralExchangeGoods
		IntegralExchangeAddress *models.IntegralExchangeAddress
		Db                      *xorm.Engine
		UpetDb                  *xorm.Engine
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			fields: fields{
				Product: &models.IntegralProduct{
					ScrmInfo: "10",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &storeHandler{
				Product:                 tt.fields.Product,
				UpetGoods:               tt.fields.UpetGoods,
				Address:                 tt.fields.Address,
				Member:                  tt.fields.Member,
				IntegralExchange:        tt.fields.IntegralExchange,
				IntegralExchangeGoods:   tt.fields.IntegralExchangeGoods,
				IntegralExchangeAddress: tt.fields.IntegralExchangeAddress,
				Db:                      tt.fields.Db,
				UpetDb:                  tt.fields.UpetDb,
			}
			if err := h.validateMallCoupon(); (err != nil) != tt.wantErr {
				t.Errorf("validateMallCoupon() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

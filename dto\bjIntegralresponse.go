package dto

//北京扣减积分请求参数
type BjIntegralResponse struct {
	Result        *BJDataResult `json:"result"`
	Message       string        `json:"message"`
	SystemError   string        `json:"systemError"`
	BusinessError string        `json:"businessError"`
	StatusCode    int           `json:"statusCode"`
	Success       bool          `json:"success"`
}

type BJDataResult struct {
	//预估积分
	Score int `json:"score"`
}

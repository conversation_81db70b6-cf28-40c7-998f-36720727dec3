package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"integral-center/models"
	"integral-center/proto/cc"
	"integral-center/proto/mc"
	"integral-center/proto/oc"
	"integral-center/utils"
	"strconv"
	"strings"
	"time"

	"github.com/maybgit/glog"
	"github.com/ppkg/kit"
)

var (
	//conn                      *amqp.Connection
	exchange = "ordercenter"
	queue    = "oc-sz-integral-notify"
)

func init() {
	//消费积分通知
	go subPayMQ()
}

/*************************************订阅积分通知到mq**********************************************/
//订阅积分通知消息
func subPayMQ() {
	defer func() {
		if err := recover(); err != nil {
			glog.Error(err)
		}
	}()

	glog.Info(fmt.Sprintf("订阅积分MQ开始！队列名，%s，Exchange：%s", queue, exchange))
	//开启链接
	conn := utils.NewMqConn()
	defer conn.Close()
	ch := utils.NewMqChannel(conn)

	defer ch.Close()
	//一次拿一个数据
	if err := ch.Qos(1, 0, false); err != nil {
		glog.Error("Rabbitmq，", queue, "，", "，", exchange, "，", err.Error())
	}

	err := ch.ExchangeDeclare(exchange, "direct", true, false, false, false, nil)
	if err != nil {
		glog.Error("RabbitMQ ExchangeDeclare fail, err : ", err)
	}
	// name// durable// delete when unused // exclusive//no-wait //arguments
	q, err := ch.QueueDeclare(queue, true, false, false, false, nil)
	if err != nil {
		glog.Error("RabbitMQ QueueDeclare fail, err : ", q.Name, err)
	}
	err = ch.QueueBind(queue, queue, exchange, false, nil)
	if err != nil {
		glog.Error("RabbitMQ QueueBind fail, err : ", q.Name, err)
	}

	if delivery, err := ch.Consume(queue, queue, false, false, false, false, nil); err != nil {
		glog.Error("RabbitMQ Consume fail, err : ", q.Name, err)
	} else {
		for d := range delivery {
			func() {
				defer func() {
					if err := recover(); err != nil {
						glog.Error(err)
						d.Reject(true)
					}
				}()
				//解析内容
				glog.Info("收到积分变化通知, mq: ", string(d.Body))
				var integralNo oc.IntegralNotify
				if err := json.Unmarshal(d.Body, &integralNo); err != nil {
					glog.Error("积分变化mq内容解析失败, mq:" + string(d.Body) + ";err: " + err.Error())
					d.Ack(false)
					return
				}
				if strings.TrimSpace(integralNo.Mobile) == "" {
					glog.Error("积分变化mq手机号不能为空, mq:" + string(d.Body))
					d.Ack(false)
					return
				}

				// V0等级不加积分
				member := &models.UpetMember{}
				//抽奖扣减积分时是没有订单的 直接查会员表
				if integralNo.Integraltype == models.IntegralTypeLuckDrawReduce {
					_, err = models.GetUpetDBConn().Table("upet_member").
						Where("scrm_user_id=?", integralNo.MemberId).Get(member)
				} else {
					_, err = models.GetUpetDBConn().Table("upet_member").Alias("m").
						Join("left", "dc_order.order_main as o", "m.scrm_user_id=o.member_id").
						Where("o.order_sn=?", integralNo.Oldordersn).Get(member)
				}

				if err != nil {
					glog.Error("sendIntegralChangeMsg upet_member error: ", err)
					return
				}
				if member.UserLevelId <= 0 {
					glog.Error("积分变化V0会员等级不加积分, mq:" + string(d.Body) + ";ScrmUserId:" + member.ScrmUserId)
					d.Ack(false)
					return
				}

				// 创建datacenter事务
				session := Engine.NewSession()
				defer session.Close()

				// 事务启动
				session.Begin()

				s := IntegralService{}
				result, insertIntegral, memberId := s.integralChange(integralNo, session)
				if len(result.ErrInfo) > 0 {
					glog.Error(result.ErrInfo)
					_, err = session.Insert(result)
					if err != nil {
						session.Rollback()
						panic(err.Error())
					}
				} else {
					var count int
					_, err := Engine.SQL("select count(*) from member_integral_push_log where order_sn = ? and integral_type = ? and refund_sn = ? and status = 1",
						integralNo.Oldordersn, integralNo.Integraltype, integralNo.Refundsn).Get(&count)
					if err != nil {
						session.Rollback()
						panic(err.Error())
					}
					if count > 0 {
						_, err = Engine.Exec("update member_integral_push_log set status=2, lastdate=current_timestamp()"+
							" where order_sn = ? and integral_type = ? and refund_sn = ?",
							integralNo.Oldordersn, integralNo.Integraltype, integralNo.Refundsn)
						if err != nil {
							session.Rollback()
							panic(err.Error())
						}
					}
				}
				session.Commit()

				d.Ack(false)
				// 积分变更通知
				go sendIntegralChangeMsg(memberId, int(integralNo.Integraltype), insertIntegral, cast.ToInt(integralNo.OrgId))

			}()
		}
	}
}

// 积分变更通知
func sendIntegralChangeMsg(memberId string, integraltype, insertIntegral int, orgId int) error {
	if insertIntegral <= 0 || memberId == "" {
		return errors.New("参数不对")
	}

	reason := ""
	if integraltype == -1 {
		insertIntegral *= -1
		reason = "积分过期"
	} else if integraltype == -2 {
		insertIntegral *= -1
		reason = "积分兑换"
	} else if integraltype == 92 {
		reason = "积分抽奖"
	} else {
		if integraltype%2 > 0 {
			reason = "销售增加积分"
		} else {
			insertIntegral *= -1
			reason = "退货扣减积分"
		}
	}

	memberIntegralInfo := &models.MemberIntegralInfo{}
	_, err := Engine.Table("member_integral_info").Where("memberid=? and org_id=?", memberId, orgId).Get(memberIntegralInfo)
	if err != nil {
		glog.Error("sendIntegralChangeMsg member_integral_info error: ", err, memberId)
		return err
	}

	member := &models.UpetMember{}
	_, err = models.GetUpetDBConn().Table("upet_member").Where("scrm_user_id=?", memberId).Get(member)
	if err != nil {
		glog.Error("sendIntegralChangeMsg upet_member error: ", err, memberId)
		return err
	}

	// 获取订阅消息模板
	subTemplate := &models.WechatSubscribeMessageTemplate{}
	_, err = models.GetDBConn().Table("wechat_subscribe_message_template").Where("template_key=? and store_id=?", "user-integral-change", orgId).Get(subTemplate)
	if err != nil {
		glog.Error("sendIntegralChangeMsg userLevelExpireTask user-integral-change error: ", err)
		return err
	}

	// 是否能推送订阅消息
	ccClient := cc.GetCustomerCenterClient()
	defer ccClient.Close()
	canRe, _ := ccClient.RPC.CanSendWechatSubscribeMessage(ccClient.Ctx, &cc.CanSendWechatSubscribeMessageReq{
		ScrmUserId:    member.ScrmUserId,
		SubscribeType: "integral",
		TemplateId:    subTemplate.TemplateId,
	})
	if canRe.Code != 200 {
		glog.Info("sendIntegralChangeMsg CanSendWechatSubscribeMessage ", memberId, kit.JsonEncode(canRe))
		return err
	}

	miniOpenId := member.WeixinMiniOpenid
	if orgId == 2 {
		miniOpenId = member.WeixinMiniOpenid2
	} else if orgId == 3 {
		miniOpenId, err = getEshopUser(cast.ToInt(memberId), 3)
		if err != nil {
			glog.Error("sendIntegralExpireMsg 获取用户福码购小程序的openid异常 error: ", err, memberId)
			return err
		}
	}

	miniprogramState := ""
	if kit.EnvIsTest() {
		miniprogramState = "trial"
	}
	// 获取变更的积分
	changeVal := fmt.Sprintf("%d积分", insertIntegral)
	curTime := kit.GetTimeNow(time.Now())
	msgClient := mc.GetMessageCenterClient()
	re, err := msgClient.RPC.SubscribeMessage(msgClient.Ctx, &mc.SubscribeMessageRequest{
		Touser:           miniOpenId,
		TemplateId:       subTemplate.TemplateId,
		Page:             "/app/points/center/my", // 小程序跳转页面
		MiniprogramState: miniprogramState,
		Data:             fmt.Sprintf(subTemplate.Content, changeVal, memberIntegralInfo.Integral, reason, curTime),
		IsJpush:          0,
		OrgId:            cast.ToInt32(orgId),
	})
	if err != nil {
		glog.Error("sendIntegralChangeMsg error: ", err, member.ScrmUserId)
		return err
	}
	if re.Code != 200 {
		glog.Error("sendIntegralChangeMsg 发送积分变更通知失败，", member.ScrmUserId, kit.JsonEncode(re))
		return errors.New(re.Message)
	}

	// 订阅数减一
	_, err = models.GetDBConn().Exec("UPDATE wechat_subscribe_message SET number=IF(number<1,0,number-1) WHERE scrm_user_id=? AND template_id=?", member.ScrmUserId, subTemplate.TemplateId)
	if err != nil {
		glog.Error("sendIntegralChangeMsg 扣减用户订阅数失败，error: ", err, memberId)
		return err
	}
	return nil
}

func getEshopUser(memberId int, orgId int) (string, error) {
	var miniOpenId string
	_, err := models.GetDBConn().SQL("SELECT weixin_mini_openid FROM eshop.users Where member_id=? AND org_id=?", memberId, orgId).Get(&miniOpenId)
	if err != nil {
		glog.Error("获取福码购小程序的用户openId失败")
		return "", err
	}
	return miniOpenId, nil
}

/*************************************发布积分通知到mq**********************************************/
// 发布消费或退货积分变化mq
func PublishIntegralChange(integral oc.IntegralNotify) {
	glog.Info("积分变化mq参数; content: ", integral)
	// 错误日志
	orderSn := integral.Ordersn
	if len(orderSn) == 0 {
		orderSn = integral.Oldordersn
	}
	var errStr strings.Builder
	errStr.WriteString("发布积分变化mq失败; 订单号：")
	errStr.WriteString(orderSn)
	errStr.WriteString(";积分类型：")
	errStr.WriteString(strconv.Itoa(int(integral.Integraltype)))
	errStr.WriteString("; err: ")

	integralJson, err := json.Marshal(&integral)
	if err != nil {
		errStr.WriteString(err.Error())
		glog.Error(errStr.String())
	}
	result := utils.PublishRabbitMQ(queue, string(integralJson), exchange)
	var mqInfo models.MqInfo
	mqInfo.Content = string(integralJson)
	mqInfo.Exchange = exchange
	mqInfo.Quene = queue
	mqInfo.Ispush = 1
	if !result {
		mqInfo.Ispush = 0
	}
	_, err = Engine.Insert(&mqInfo)
	if err != nil {
		errStr.WriteString(err.Error())
		glog.Error(errStr.String())
	}
}

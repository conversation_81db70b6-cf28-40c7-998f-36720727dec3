package models

import (
	"errors"
	"time"
)

type UpetGoods struct {
	GoodsId              int32   `xorm:"not null pk autoincr comment('商品id(SKU)') INT(10)"`
	GoodsCommonid        int32   `xorm:"not null comment('商品公共表id') index INT(10)"`
	GoodsName            string  `xorm:"not null comment('商品名称（+规格名称）') VARCHAR(50)"`
	GoodsJingle          string  `xorm:"default '' comment('商品广告词') VARCHAR(150)"`
	StoreId              int32   `xorm:"not null comment('店铺id') INT(10)"`
	StoreName            string  `xorm:"not null comment('店铺名称') VARCHAR(50)"`
	GcId                 int32   `xorm:"not null comment('商品分类id') INT(10)"`
	GcId1                int32   `xorm:"not null comment('一级分类id') INT(10) 'gc_id_1'"`
	GcId2                int32   `xorm:"not null comment('二级分类id') INT(10) 'gc_id_2'"`
	GcId3                int32   `xorm:"not null comment('三级分类id') INT(10) 'gc_id_3'"`
	BrandId              int32   `xorm:"default 0 comment('品牌id') INT(10)"`
	GoodsPrice           float32 `xorm:"not null comment('商品价格') index DECIMAL(10,2)"`
	GoodsPromotionPrice  float32 `xorm:"not null comment('商品促销价格') DECIMAL(10,2)"`
	GoodsPromotionType   int32   `xorm:"not null default 0 comment('促销类型 0无促销，1团购，2限时折扣 12水印') TINYINT(3)"`
	GoodsMarketprice     float32 `xorm:"not null comment('市场价') DECIMAL(10,2)"`
	GoodsSerial          string  `xorm:"default '' comment('商品货号') index VARCHAR(50)"`
	GoodsStorageAlarm    int32   `xorm:"not null comment('库存报警值') TINYINT(3)"`
	GoodsBarcode         string  `xorm:"default '' comment('商品条形码') VARCHAR(20)"`
	GoodsClick           int32   `xorm:"not null default 0 comment('商品点击数量') INT(10)"`
	GoodsSalenum         int32   `xorm:"not null default 0 comment('销售数量') INT(10)"`
	GoodsCollect         int32   `xorm:"not null default 0 comment('收藏数量') INT(10)"`
	SpecName             string  `xorm:"not null comment('规格名称') VARCHAR(255)"`
	GoodsSpec            string  `xorm:"not null comment('商品规格序列化') TEXT"`
	GoodsStorage         int32   `xorm:"not null default 0 comment('商品库存') INT(10)"`
	GoodsVirtualStorage  int32   `xorm:"default 0 comment('虚拟库存') INT(10)"`
	GoodsImage           string  `xorm:"not null comment('商品主图') VARCHAR(5000)"`
	GoodsBody            string  `xorm:"not null comment('商品描述') TEXT"`
	MobileBody           string  `xorm:"not null comment('手机端商品描述') TEXT"`
	GoodsState           int32   `xorm:"not null comment('商品状态 0下架，1正常，10违规（禁售）') TINYINT(3)"`
	GoodsVerify          int32   `xorm:"not null comment('商品审核 1通过，0未通过，10审核中') TINYINT(3)"`
	GoodsAddtime         int32   `xorm:"not null comment('商品添加时间') INT(10)"`
	GoodsEdittime        int32   `xorm:"not null comment('商品编辑时间') INT(10)"`
	Areaid1              int32   `xorm:"not null comment('一级地区id') INT(10) 'areaid_1'"`
	Areaid2              int32   `xorm:"not null comment('二级地区id') INT(10) 'areaid_2'"`
	ColorId              int32   `xorm:"not null default 0 comment('颜色规格id') INT(10)"`
	TransportId          int32   `xorm:"not null comment('运费模板id') MEDIUMINT(8)"`
	GoodsFreight         float32 `xorm:"not null default 0.00 comment('运费 0为免运费') DECIMAL(10,2)"`
	GoodsVat             int32   `xorm:"not null default 0 comment('是否开具增值税发票 1是，0否') TINYINT(3)"`
	GoodsCommend         int32   `xorm:"not null default 0 comment('商品推荐 1是，0否 默认0') TINYINT(3)"`
	GoodsStcids          string  `xorm:"default '' comment('店铺分类id 首尾用,隔开') VARCHAR(255)"`
	EvaluationGoodStar   int32   `xorm:"not null default 5 comment('好评星级') TINYINT(3)"`
	EvaluationCount      int32   `xorm:"not null default 0 comment('评价数') INT(10)"`
	IsVirtual            int32   `xorm:"not null default 0 comment('是否为虚拟商品 1是，0否') TINYINT(3)"`
	ValidityType         int32   `xorm:"not null comment('1:指定时间2:相对时间') TINYINT(1)"`
	VirtualIndate        int32   `xorm:"not null comment('虚拟商品有效期') INT(10)"`
	VirtualLimit         int32   `xorm:"not null comment('虚拟商品购买上限') INT(10)"`
	VirtualInvalidRefund int32   `xorm:"not null default 1 comment('是否允许过期退款， 1是，0否') TINYINT(3)"`
	IsFcode              int32   `xorm:"not null default 0 comment('是否为F码商品 1是，0否') TINYINT(4)"`
	IsPresell            int32   `xorm:"not null default 0 comment('是否是预售商品 1是，0否') TINYINT(3)"`
	PresellDeliverdate   int32   `xorm:"not null default 0 comment('预售商品发货时间') INT(11)"`
	IsBook               int32   `xorm:"not null default 0 comment('是否为预定商品，1是，0否') TINYINT(4)"`
	BookDownPayment      float32 `xorm:"not null default 0.00 comment('定金金额') DECIMAL(10,2)"`
	BookFinalPayment     float32 `xorm:"not null default 0.00 comment('尾款金额') DECIMAL(10,2)"`
	BookDownTime         int32   `xorm:"not null default 0 comment('预定结束时间') INT(11)"`
	BookBuyers           int32   `xorm:"default 0 comment('预定人数') MEDIUMINT(9)"`
	HaveGift             int32   `xorm:"not null default 0 comment('是否拥有赠品') TINYINT(3)"`
	IsOwnShop            int32   `xorm:"not null default 0 comment('是否为平台自营') TINYINT(3)"`
	IsChain              int32   `xorm:"not null default 0 comment('是否为门店商品 1是，0否') TINYINT(3)"`
	GoodsTransV          float32 `xorm:"not null default 0.00 comment('重量或体积') DECIMAL(10,2)"`
	IsDis                int32   `xorm:"not null default 0 comment('是否分销') TINYINT(3)"`
	DisCommisRate        float32 `xorm:"not null default 0.00 comment('当前佣金比例') DECIMAL(4,2)"`
	DisActivityId        int32   `xorm:"not null default 0 comment('参加限时佣金活动的id') INT(10)"`
	DisNormalCommisRate  float32 `xorm:"not null default 0.00 comment('日常佣金比例') DECIMAL(4,2)"`
	DisAddTime           int32   `xorm:"not null default 0 comment('分销添加时间') INT(10)"`
	IsBatch              int32   `xorm:"default 0 comment('是否批发商品 0零售  1批发') TINYINT(1)"`
	BatchPrice           string  `xorm:"comment('批发阶梯价') TEXT"`
	GoodsInv             int32   `xorm:"not null default 1 comment('是否开发票') TINYINT(3)"`
	MemberPrice1         float32 `xorm:"not null default 0.00 comment('会员等级价v1') DECIMAL(10,2) 'member_price_1'"`
	GoodsLimit           int32   `xorm:"default 0 comment('限购：0不限购') INT(11)"`
	GoodsRecommendNum    int32   `xorm:"comment('APP推荐次数') INT(11)"`
	GSearchStatus        int32   `xorm:"not null default 0 comment('是否是允许搜索商品 1否，0是') TINYINT(1)"`
	GCType               int32   `xorm:"default 0 comment('1猫站，2狗站') TINYINT(4)"`
	Freight              int32   `xorm:"default 0 comment('是否包邮0为不包邮，2为包邮') TINYINT(1)"`
	ChainId              int32   `xorm:"default 0 comment('记录商品归属门店id') INT(11)"`
	RegionId             int32   `xorm:"default 0 comment('大区标识') INT(11)"`
	GoodPercent          int32   `xorm:"default 0 comment('好评率') TINYINT(4)"`
	GoodsSku             string  `xorm:"comment('中心货号') index VARCHAR(100)"`
	GoodsSkuType         int32   `xorm:"default 1 comment('货号类型 1全渠道 2管易 3门店') TINYINT(1)"`
	IsVip                int32   `xorm:"default 0 comment('是否限制会员卡用户购买，0否，1是') TINYINT(1)"`
	IsBzk                int32   `xorm:"default 0 comment('是否限制保障卡用户购买，0否，1是') TINYINT(1)"`
	RelevanceId          int32   `xorm:"default 0 comment('关联索引ID') INT(11)"`
	GoodsType            int32   `xorm:"default 0 comment('0普通商品，1实实组合，2虚虚组合，3虚实组合') TINYINT(4)"`
	WarehouseType        int32   `xorm:"comment('药品仓类型：0:默认否, 1:巨星药品仓') TINYINT(4)"`
	IsOpenVirtualStock   int32   `xorm:"default 0 comment('是否开启虚拟库存，0默认关闭，1-开启') TINYINT(4)"`
	DisWrite             string  `xorm:"default '' comment('分销文案') VARCHAR(100)"`
	IsIntelGoods         int32   `xorm:"default 0 comment('是否互联网医疗商品（1是，0否）') TINYINT(4)"`
}

// GetValidVirtualGoods 获取有效的虚拟商品
func GetValidVirtualGoods(id int32, orgId int32) (g *UpetGoods, err error) {
	g = new(UpetGoods)
	if has, err := GetUpetDBConn().Table("upet_goods").Where("goods_id = ? AND store_id=?", id, orgId).Get(g); err != nil {
		return nil, errors.New("商品查询出错 " + err.Error())
	} else if !has {
		return nil, errors.New("商品不存在")
	}
	if g.GoodsState != 1 {
		return nil, errors.New("商品已下架")
	}
	if g.GoodsStorage < 1 {
		return nil, errors.New("商品库存不足")
	}
	if g.IsVirtual != 1 {
		return nil, errors.New("商品类型错误")
	}
	// 指定时间有效
	if g.ValidityType == 1 && g.VirtualIndate < int32(time.Now().Unix()) {
		return nil, errors.New("商品已过期")
	}

	return g, nil
}

// GetValidGoods 获取有效的商品
func GetValidGoods(id int32, orgId int32) (g *UpetGoods, err error) {
	g = new(UpetGoods)
	if has, err := GetUpetDBConn().Table("upet_goods").Where("goods_id = ? AND store_id = ?", id, orgId).Get(g); err != nil {
		return nil, errors.New("商品查询出错 " + err.Error())
	} else if !has {
		return nil, errors.New("商品不存在")
	}
	if g.GoodsState != 1 {
		return nil, errors.New("商品已下架")
	}
	if g.GoodsStorage < 1 {
		return nil, errors.New("商品库存不足")
	}

	return g, nil
}

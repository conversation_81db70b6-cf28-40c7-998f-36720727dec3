package utils

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"log"
	"math/rand"
	"strings"
	"time"

	"github.com/google/uuid"
)

//获取随机字符串，指定长度
func GetRandomString(l int) string {
	str := "0123456789abcdefghijklmnopqrstuvwxyz"
	bytes := []byte(str)
	result := []byte{}
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	for i := 0; i < l; i++ {
		result = append(result, bytes[r.Intn(len(bytes))])
	}
	return string(result)
}

//PrintJSON 将struct序列化json打印日志
func PrintJSON(inter interface{}) {
	bt, _ := json.Marshal(inter)
	log.Println("json", string(bt))
}

//生成32位md5字串
func GetMd5String(s string) string {
	h := md5.New()
	h.Write([]byte(s))
	return hex.EncodeToString(h.Sum(nil))
}

//生成32位Guid字串
func GetGuid() string {
	return strings.ReplaceAll(uuid.New().String(), "-", "")
}

//生成36位Guid字串
func GetGuid36() string {
	return uuid.New().String()
}

package models

type UpetAddress struct {
	AddressId   int32   `xorm:"not null pk autoincr comment('地址ID') MEDIUMINT(8)"`
	MemberId    int32   `xorm:"not null default 0 comment('会员ID') index MEDIUMINT(8)"`
	TrueName    string  `xorm:"not null comment('会员姓名') VARCHAR(50)"`
	AreaId      int32   `xorm:"not null default 0 comment('地区ID') MEDIUMINT(8)"`
	CityId      int32   `xorm:"comment('市级ID') MEDIUMINT(9)"`
	AreaInfo    string  `xorm:"not null default '' comment('地区内容') VARCHAR(255)"`
	Address     string  `xorm:"not null comment('地址') VARCHAR(255)"`
	TelPhone    string  `xorm:"comment('座机电话') VARCHAR(20)"`
	MobPhone    string  `xorm:"comment('手机电话') VARCHAR(15)"`
	IsDefault   int32   `xorm:"not null default 0 comment('1默认收货地址') TINYINT(4)"`
	DlypId      int32   `xorm:"default 0 comment('自提点ID') INT(11)"`
	AreaLat     float32 `xorm:"default 0.0000000000000 comment('纬度') DECIMAL(17,13)"`
	AreaLng     float32 `xorm:"default 0.0000000000000 comment('经度') DECIMAL(17,13)"`
	AreaTxlat   float32 `xorm:"default 0.0000000000000 comment('腾讯纬度') DECIMAL(17,13)"`
	AreaTxlng   float32 `xorm:"default 0.0000000000000 comment('腾讯经度') DECIMAL(17,13)"`
	Isdeal      int32   `xorm:"default 0 comment('0默认1已处理腾讯坐标转换') TINYINT(1)"`
	AddressDesc string  `xorm:"default '' comment('地图地址说明') VARCHAR(150)"`
	HouseInfo   string  `xorm:"default '' comment('门牌号') VARCHAR(100)"`
	AreaAdcode  int32   `xorm:"default 0 comment('城市编码') INT(11)"`
	MapAddress  string  `xorm:"comment('地图地址') VARCHAR(255)"`
}

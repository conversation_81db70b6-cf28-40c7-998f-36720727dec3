package services

import (
	"context"
	"github.com/go-xorm/xorm"
	"github.com/maybgit/glog"
	"integral-center/dto"
	"integral-center/models"
	"integral-center/proto/igc"
	"integral-center/proto/oc"
	"integral-center/utils"
	"reflect"
	"testing"
	"time"
)

func TestBatchImportErrMq(t *testing.T) {
	err := BatchImportErrMq()
	if err != nil {
		t.Errorf("BatchImportErrMq() error = %v", err)
		return
	}
}

func TestIntegralService_GetIntegralListByMemberId(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		req *oc.GetIntegralListByMemberIdReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.GetIntegralListByMemberIdRes
		wantErr bool
	}{
		{
			name: "根据用户id等参数获取数据",
			args: args{
				ctx: nil,
				req: &oc.GetIntegralListByMemberIdReq{
					PageIndex: 1,
					PageSize:  10,
					MemberId:  "cbe4707e007643da8eeec9af577c8859",
					OrgId:     1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &IntegralService{
				BaseService: tt.fields.BaseService,
			}
			got, err := s.GetIntegralListByMemberId(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetIntegralListByMemberId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetIntegralListByMemberId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIntegralService_RecoverUserIntegral(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		req *oc.RecoverUserIntegralReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *oc.BaseResponse
		wantErr bool
	}{
		{name: "驳回退款申请，还原积分",
			args: args{
				ctx: nil,
				req: &oc.RecoverUserIntegralReq{
					OrderSn:       "4100000005609193",
					MemberId:      "25ff07e7bd9f433fb519f6cd9997253a",
					RefundOrderSn: "50000001992",
				},
			}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &IntegralService{
				BaseService: tt.fields.BaseService,
			}
			got, err := s.RecoverUserIntegral(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("RecoverUserIntegral() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RecoverUserIntegral() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIntegralService_Recompute(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		ctx context.Context
		in  *igc.IntegralRecomputeReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *oc.BaseRes
		wantErr bool
	}{
		{
			args: args{in: &igc.IntegralRecomputeReq{
				ScrmId:    "971c0af12f1d4d63a90b818f73c8db72,971c0af12f1d4d63a90b818f73c8db72,971c0af12f1d4d63a90b818f73c8db72,971c0af12f1d4d63a90b818f73c8db72,971c0af12f1d4d63a90b818f73c8db72,971c0af12f1d4d63a90b818f73c8db72,971c0af12f1d4d63a90b818f73c8db72",
				Timestamp: 1650507732,
				StartId:   0,
				EndId:     0,
				RecordLog: 1,
				Sign:      "DD8738C100CC5198C9203C604778E536",
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &IntegralService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, err := s.Recompute(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("Recompute() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("Recompute() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestIntegralService_integralChange(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	type args struct {
		integralNo oc.IntegralNotify
		session    *xorm.Session
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *models.MemberIntegralPushLog
		want1  int
		want2  string
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				integralNo: oc.IntegralNotify{
					Oldordersn:   "luck-draw-223",
					Integraltype: 92,
					Notifytime:   "2022-10-26 15:46:11",
					Integral:     1,
					MemberId:     "5c1ad0f2e4e94f39a43c263c9b287d6d",
				},
				session: Engine.NewSession(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &IntegralService{
				BaseService: tt.fields.BaseService,
			}
			got, got1, got2 := s.integralChange(tt.args.integralNo, tt.args.session)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("integralChange() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("integralChange() got1 = %v, want %v", got1, tt.want1)
			}
			if got2 != tt.want2 {
				t.Errorf("integralChange() got2 = %v, want %v", got2, tt.want2)
			}
		})
	}
}

func TestIntegralService_DoAllUser(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
		{name: "查询要处理的用户"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &IntegralService{
				BaseService: tt.fields.BaseService,
			}
			s.DoAllUser("1")
			//s.DoAllUserKafka("1")
		})
	}
}

func TestIntegralService_Expire(t *testing.T) {
	type fields struct {
		BaseService BaseService
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
		{name: "积分过期任务"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &IntegralService{
				BaseService: tt.fields.BaseService,
			}

			modelMqInfo := dto.IntegralExpire{}
			modelMqInfo.OrgId = 1
			modelMqInfo.Memberid = "f77e65ca84814269a7f76087cd3af021"
			modelMqInfo.BeTime = "2022-06-21 00:00:00"
			modelMqInfo.EndTime = "2022-06-28 00:00:00"

			dbConn := newDbConn()
			userExpireIntegral := 0 //用户需要过期的积分数
			dbConn.SQL(" SELECT SUM(surplusintegralcount) FROM member_integral_record WHERE surplusintegralcount > 0 AND createdate>=? and createdate < ? and  memberid = ? and org_id=?", modelMqInfo.BeTime, modelMqInfo.EndTime, modelMqInfo.Memberid, modelMqInfo.OrgId).Get(&userExpireIntegral)

			session := dbConn.NewSession()
			defer session.Close()
			session.Begin()
			errorCount := 0

			if userExpireIntegral > 0 {
				MemberIntegralRecord := models.MemberIntegralRecord{}
				//查询用户当前积分和用户当前会员等级
				_, err := dbConn.SQL(`SELECT a.integral integralcount,IFNULL(b.user_level_id,0) user_level_id
           FROM member_integral_info a left join upetmart.upet_member b on a.memberid=b.scrm_user_id where memberid = ? and org_id=?`, modelMqInfo.Memberid, modelMqInfo.OrgId).Get(&MemberIntegralRecord)
				if err != nil {
					errorCount++
					session.Rollback()
					glog.Error("DoAllUserDetail 用户积分查询出错： ", err.Error(), " 当前用户：", modelMqInfo.Memberid, modelMqInfo.OrgId)
					return
				}

				//更新单个用户的积分数（如果小于则清0）
				if _, updateInfoErr := session.Exec("update `datacenter`.member_integral_info set integral = IF(integral < ?, 0, integral - ?) where memberid = ? and org_id=?", userExpireIntegral, userExpireIntegral, modelMqInfo.Memberid, modelMqInfo.OrgId); updateInfoErr != nil {
					glog.Error("UserExpireDetail 更新用户积分出错", modelMqInfo.Memberid, updateInfoErr.Error())
					session.Rollback()
					errorCount++
					return
					//d.Nack(false, true)
				}
				itemuserExpireIntegral := 0
				//如果剩余积分小于过期积分，那么就全部过期，过期积分就等于剩余积分
				if MemberIntegralRecord.Integralcount < userExpireIntegral {
					itemuserExpireIntegral = MemberIntegralRecord.Integralcount
				} else {
					itemuserExpireIntegral = userExpireIntegral
				}

				//插入过期积分记录
				id := utils.GetGuid()
				model := &models.MemberIntegralRecord{
					Integralid:           id,
					Orderid:              id,
					Memberid:             modelMqInfo.Memberid,
					Integralcount:        itemuserExpireIntegral,
					Integraltype:         82,
					Integralreason:       "积分过期",
					Ischeck:              1,
					Createdate:           time.Now(),
					Lasttime:             time.Now(),
					Payamount:            0,
					Surplusintegralcount: 0,
					OrgId:                modelMqInfo.OrgId,
					UserLevelId:          MemberIntegralRecord.UserLevelId,
				}
				_, err = session.Table("member_integral_record").Insert(model)
				if err != nil {
					glog.Error("UserExpireDetail 插入过期积分记录出错： ", err.Error(), " 当前用户：", modelMqInfo.Memberid)
					errorCount++
					session.Rollback()
					return
				}
			}

			//更新本次分页的积分流水，剩余积分清0
			updateRecordSql := "update `datacenter`.member_integral_record set surplusintegralcount = 0 " +
				"where  surplusintegralcount > 0 AND createdate>=? and createdate < ? and  memberid = ? and org_id=?"
			if _, updateRecordErr := session.Exec(updateRecordSql, modelMqInfo.BeTime, modelMqInfo.EndTime, modelMqInfo.Memberid, modelMqInfo.OrgId); updateRecordErr != nil {
				session.Rollback()
				glog.Error("UserExpireDetail 更新用户积分记录出错", modelMqInfo.Memberid, updateRecordErr.Error())
				errorCount++
				return
				//d.Nack(false, true)
				//return "success", nil
			}

			s.Expire()
		})
	}
}

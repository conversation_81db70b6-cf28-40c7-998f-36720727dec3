package integral

import "testing"

func TestRecompute(t *testing.T) {
	type args struct {
		scrmId string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			args: args{scrmId: "971c0af12f1d4d63a90b818f73c8db72"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := Recompute(tt.args.scrmId, true); (err != nil) != tt.wantErr {
				t.Errorf("Recompute() error = %v, wantErr %v", err, tt.wantErr)
			}
			t.Log("yes")
		})
	}
}

func TestRecomputeByRange(t *testing.T) {
	type args struct {
		startId   int32
		endId     int32
		recordLog bool
	}
	tests := []struct {
		name string
		args args
	}{
		{
			args: args{
				startId:   1,
				endId:     100,
				recordLog: false,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			RecomputeByRange(tt.args.startId, tt.args.endId, tt.args.recordLog)
		})
	}
}

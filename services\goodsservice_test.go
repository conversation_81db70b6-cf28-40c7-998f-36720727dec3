package services

import (
	"context"
	"fmt"
	"github.com/golang/protobuf/ptypes/wrappers"
	"integral-center/proto/igc"
	"testing"
)

func TestGoodsService_List(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *igc.GoodsListRequest
	}
	tests := []struct {
		name    string
		args    args
		wantOut *igc.GoodsListResponse
		wantErr bool
	}{
		{
			name: "积分商品-下架列表",
			args: args{
				ctx: nil,
				in: &igc.GoodsListRequest{
					Show: "0",
				},
			},
		},
	}
	fmt.Println(tests)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			g := GoodsService{}
			gotOut, err := g.List(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("List() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && gotOut.Code != 200 {
				t.<PERSON><PERSON>("List() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestGoodsService_Detail(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *igc.GoodsIdRequest
	}
	tests := []struct {
		name    string
		args    args
		wantOut *igc.GoodsDetailResponse
		wantErr bool
	}{
		{
			args: args{
				ctx: nil,
				in: &igc.GoodsIdRequest{
					Id: 1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			g := GoodsService{}
			gotOut, err := g.Detail(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("Detail() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && gotOut.Data.Id != tt.args.in.Id {
				t.Errorf("Detail() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestGoodsService_Store(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *igc.GoodsDetail
	}
	tests := []struct {
		name    string
		args    args
		wantOut *igc.GoodsResponse
		wantErr bool
	}{
		{
			name: "",
			args: args{
				ctx: nil,
				in: &igc.GoodsDetail{
					Type:        3,
					GoodType:    4,
					Name:        "顽皮无谷低敏小型犬成犬粮 2kg",
					Price:       118.00,
					Points:      11800,
					Serial:      "YYZN185",
					Image:       "05993263318908620.jpg",
					Storage:     100,
					Body:        "<div class=\"__kindeditor_paste__\">\n\t<img alt=\"\" class=\"\" src=\"https://oss.upetmart.com/www/shop/store/repgoods/100411/62bf06f8b1d44f67508cf9f9bf97a5bb.jpg\" /><br />\n<img alt=\"\" class=\"\" src=\"https://oss.upetmart.com/www/shop/store/repgoods/100411/f792dd55ca89e1a7a960af945a9285fe.jpg\" /><br />\n<img alt=\"\" class=\"\" src=\"https://oss.upetmart.com/www/shop/store/repgoods/100411/1e093e8cad45df7ca291ed46383598ba.jpg\" /><br />\n<img alt=\"\" class=\"\" src=\"https://oss.upetmart.com/www/shop/store/repgoods/100411/4b253fe24bcade4f60ae4de81fd67243.jpg\" /><br />\n<img alt=\"\" class=\"\" src=\"https://oss.upetmart.com/www/shop/store/repgoods/100411/62b8623776445db4b95a85bc3123b813.jpg\" /><br />\n<img alt=\"\" class=\"\" src=\"https://oss.upetmart.com/www/shop/store/repgoods/100411/1660a1787548bba7ddc6e70fc33afa42.jpg\" /><br />\n<img alt=\"\" class=\"\" src=\"https://oss.upetmart.com/www/shop/store/repgoods/100411/3d6245be2c752c1b39eff863dbc79cbc.jpg\" /><br />\n<img alt=\"\" class=\"\" src=\"https://oss.upetmart.com/www/shop/store/repgoods/100411/9d33dc1116a73aa13d84ea341178ef44.jpg\" /><br />\n<img alt=\"\" class=\"\" src=\"https://oss.upetmart.com/www/shop/store/repgoods/100411/df39e731c456ad9c2437bffdc31d0afb.jpg\" /><br />\n<img alt=\"\" class=\"\" src=\"https://oss.upetmart.com/www/shop/store/repgoods/100411/766ef35276cac7b5a4d16d6aadd6f675.jpg\" /><br />\n<img alt=\"\" class=\"\" src=\"https://oss.upetmart.com/www/shop/store/repgoods/100411/44cd8e624e32f9669f2c019819a7d0ff.jpg\" /><br />\n<img alt=\"\" class=\"\" src=\"https://oss.upetmart.com/www/shop/store/repgoods/100411/8c9bacf2605c51babb2c30fd58d729b0.jpg\" /><br />\n<img alt=\"\" class=\"\" src=\"https://oss.upetmart.com/www/shop/store/repgoods/100411/1a832c2a59f7abe6c607373bb4c23465.jpg\" /><br />\n<img alt=\"\" class=\"\" src=\"https://oss.upetmart.com/www/shop/store/repgoods/100411/d9de19b50a50d8402c589988804adaf7.jpg\" /><br />\n<img alt=\"\" class=\"\" src=\"https://oss.upetmart.com/www/shop/store/repgoods/100411/752c070aac00d001b9dc55d061eef7b4.jpg\" /> \n</div>",
					StartTime:   "2021-10-10 10:10:10",
					EndTime:     "2021-10-10 11:10:10",
					IsLimitTime: 1,
					ScrmInfo:    1023427002,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			g := GoodsService{}
			gotOut, err := g.Store(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("Store() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && gotOut.Code != 200 {
				t.Errorf("List() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestGoodsService_Patch(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *igc.GoodsPatchRequest
	}
	tests := []struct {
		name    string
		args    args
		wantOut *igc.GoodsResponse
		wantErr bool
	}{
		{
			args: args{
				ctx: nil,
				in: &igc.GoodsPatchRequest{
					Id:      80,
					Show:    &wrappers.Int32Value{Value: 1},
					Commend: &wrappers.Int32Value{Value: 1},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			g := GoodsService{}
			gotOut, err := g.Patch(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("Patch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err != nil && gotOut.Code != 200 {
				t.Errorf("Patch() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestGoodsService_AWList(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *igc.AWGoodsListRequest
	}
	tests := []struct {
		name    string
		args    args
		wantOut *igc.AWGoodsListResponse
		wantErr bool
	}{
		{
			args: args{
				ctx: nil,
				in:  &igc.AWGoodsListRequest{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			g := GoodsService{}
			gotOut, err := g.AWList(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("AWList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err != nil && gotOut.Code != 200 {
				t.Errorf("AWList() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestGoodsService_AWDetail(t *testing.T) {
	type args struct {
		ctx     context.Context
		request *igc.GoodsIdRequest
	}
	tests := []struct {
		name    string
		args    args
		wantOut *igc.GoodsDetailResponse
		wantErr bool
	}{
		{
			args: args{
				ctx: nil,
				request: &igc.GoodsIdRequest{
					Id:     81,
					ScrmId: "1",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			g := GoodsService{}
			gotOut, err := g.AWDetail(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("AWDetail() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err != nil && gotOut.Code != 200 {
				t.Errorf("AWDetail() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

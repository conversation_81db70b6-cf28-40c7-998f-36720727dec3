package order

import (
	"github.com/go-xorm/xorm"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
	"integral-center/helpers"
	"integral-center/models"
)

var pageSize = 10000
var page = 1

type exportExtends struct {
	models.IntegralExchange        `xorm:"extends"`
	models.IntegralExchangeAddress `xorm:"extends"`
	GoodsNames                     string
}

// Export 订单导出任务
func Export(taskId int32, session *xorm.Session) {
	file := excelize.NewFile()
	writer, _ := file.NewStreamWriter("Sheet1")

	_ = writer.SetRow("A1", []interface{}{
		"订单编号", "会员名称", "兑换积分", "兑换时间", "状态", "类型",
		"优惠券码", "过期时间", "物流单号", "物流编码", "发货时间", "完成时间",
		"来源", "订单留言", "收货人", "联系电话", "会员手机", "收货地址", "商品名称",
	})

	session.Select("e.*,a.*,(select group_concat(goods_name) from dc_order.integral_exchange_goods eg where eg.exchange_id = e.id) as goods_names")

	row := 0
	for {
		var extends []*exportExtends

		if err := session.Clone().Limit(pageSize, pageSize*(page-1)).Find(&extends); err != nil {
			failHandle(taskId, "查询数据出错 "+err.Error())
			return
		}
		for _, extend := range extends {
			_ = writer.SetRow("A"+cast.ToString(row+2), extendToRow(extend))
			row++
		}
		// 数据结束
		if len(extends) < pageSize {
			break
		}
		// 下一页
		page++
	}

	if err := writer.Flush(); err != nil {
		failHandle(taskId, "保存文件失败 "+err.Error())
		return
	}

	// 上传excel文件
	if url, err := helpers.UploadExcelToQiNiu(file, ""); err != nil {
		failHandle(taskId, err.Error())
	} else {
		updateResult(taskId, map[string]interface{}{
			"state": models.IETaskStateSuccess,
			"url":   url,
		})
	}

	return
}

// 错误处理
func failHandle(taskId int32, msg string) {
	updateResult(taskId, map[string]interface{}{
		"state":  models.IETaskStateFail,
		"result": msg,
	})
}

// 更新导出结果
func updateResult(taskId int32, update map[string]interface{}) {
	upetDb := models.GetDBConn()
	_, err := upetDb.Table("dc_order.integral_exchange_task").Where("id = ?", taskId).Update(update)

	if err != nil {
		glog.Info("积分商城 OrderExport 任务Id：", taskId, "，结果保存失败：", err.Error())
	}
}

// 订单转导出行
func extendToRow(extend *exportExtends) []interface{} {
	return []interface{}{
		extend.OrderSn,
		extend.UserName,
		extend.IntegralTotal,
		helpers.TimestampToDateTime(extend.AddTime),
		extend.GetStateText(),
		extend.GetTypeText(),
		extend.CouponCode,
		helpers.TimestampToDateTime(extend.CouponEndTime),
		extend.ShippingCode,
		extend.ShippingEcode,
		helpers.TimestampToDateTime(extend.ShippingTime),
		helpers.TimestampToDateTime(extend.FinnshedTime),
		extend.GetFromText(),
		extend.Remark,
		extend.Name,
		extend.Mobile,
		extend.UserMobile,
		extend.AreaInfo + " " + extend.Address,
		extend.GoodsNames,
	}
}

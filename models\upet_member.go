package models

import (
	"time"
)

type UpetMember struct {
	MemberId              int32     `xorm:"not null pk autoincr comment('会员id') INT(11)"`
	ScrmUserId            string    `xorm:"default '' comment('用户ID作为唯一标示') index VARCHAR(32)"`
	MemberName            string    `xorm:"comment('会员名称') unique VARCHAR(50)"`
	MemberTruename        string    `xorm:"comment('真实姓名') VARCHAR(20)"`
	MemberAvatar          string    `xorm:"comment('会员头像') VARCHAR(255)"`
	MemberWxavatar        string    `xorm:"comment('微信头像') VARCHAR(255)"`
	MemberSex             int32     `xorm:"comment('会员性别') TINYINT(1)"`
	MemberBirthday        time.Time `xorm:"comment('生日') DATE"`
	MemberPasswd          string    `xorm:"comment('会员密码') VARCHAR(32)"`
	MemberPaypwd          string    `xorm:"comment('支付密码') CHAR(32)"`
	MemberEmail           string    `xorm:"default '' comment('会员邮箱') VARCHAR(100)"`
	MemberEmailBind       int32     `xorm:"not null default 0 comment('0未绑定1已绑定') TINYINT(4)"`
	MemberMobile          string    `xorm:"comment('手机号') index VARCHAR(11)"`
	MemberMobileBind      int32     `xorm:"not null default 0 comment('0未绑定1已绑定') TINYINT(4)"`
	MemberQq              string    `xorm:"comment('qq') VARCHAR(100)"`
	MemberWw              string    `xorm:"comment('阿里旺旺') VARCHAR(100)"`
	MemberLoginNum        int32     `xorm:"not null default 1 comment('登录次数') INT(11)"`
	MemberTime            string    `xorm:"not null comment('会员注册时间') VARCHAR(10)"`
	MemberLoginTime       string    `xorm:"not null comment('当前登录时间') VARCHAR(10)"`
	MemberOldLoginTime    string    `xorm:"not null comment('上次登录时间') VARCHAR(10)"`
	MemberLoginIp         string    `xorm:"comment('当前登录ip') VARCHAR(20)"`
	MemberOldLoginIp      string    `xorm:"comment('上次登录ip') VARCHAR(20)"`
	MemberQqopenid        string    `xorm:"comment('qq互联id') VARCHAR(100)"`
	MemberQqinfo          string    `xorm:"comment('qq账号相关信息') TEXT"`
	MemberSinaopenid      string    `xorm:"comment('新浪微博登录id') VARCHAR(100)"`
	MemberSinainfo        string    `xorm:"comment('新浪账号相关信息序列化值') TEXT"`
	WeixinUnionid         string    `xorm:"comment('微信用户统一标识') VARCHAR(50)"`
	WeixinInfo            string    `xorm:"comment('微信用户相关信息') VARCHAR(255)"`
	MemberPoints          int32     `xorm:"not null default 0 comment('会员积分') INT(11)"`
	AvailablePredeposit   float32   `xorm:"not null default 0.00 comment('预存款可用金额') DECIMAL(10,2)"`
	FreezePredeposit      float32   `xorm:"not null default 0.00 comment('预存款冻结金额') DECIMAL(10,2)"`
	AvailableRcBalance    float32   `xorm:"not null default 0.00 comment('可用充值卡余额') DECIMAL(10,2)"`
	FreezeRcBalance       float32   `xorm:"not null default 0.00 comment('冻结充值卡余额') DECIMAL(10,2)"`
	InformAllow           int32     `xorm:"not null default 1 comment('是否允许举报(1可以/2不可以)') TINYINT(1)"`
	IsBuy                 int32     `xorm:"not null default 1 comment('会员是否有购买权限 1为开启 0为关闭') TINYINT(1)"`
	IsAllowtalk           int32     `xorm:"not null default 1 comment('会员是否有咨询和发送站内信的权限 1为开启 0为关闭') TINYINT(1)"`
	MemberState           int32     `xorm:"not null default 1 comment('会员的开启状态 1为开启 0为关闭') TINYINT(1)"`
	MemberSnsvisitnum     int32     `xorm:"not null default 0 comment('sns空间访问次数') INT(11)"`
	MemberAreaid          int32     `xorm:"comment('地区ID') INT(11)"`
	MemberCityid          int32     `xorm:"comment('城市ID') INT(11)"`
	MemberProvinceid      int32     `xorm:"comment('省份ID') INT(11)"`
	MemberAreainfo        string    `xorm:"comment('地区内容') VARCHAR(255)"`
	MemberPrivacy         string    `xorm:"comment('隐私设定') TEXT"`
	MemberExppoints       int32     `xorm:"not null default 0 comment('会员经验值') INT(11)"`
	TradAmount            float32   `xorm:"default 0.00 comment('可提现金额') DECIMAL(12,2)"`
	AuthMessage           string    `xorm:"comment('审核意见') VARCHAR(255)"`
	DistriState           int32     `xorm:"default 0 comment('分销状态 0未申请 1待审核 2已通过 3未通过 4清退 5退出') index TINYINT(1)"`
	BillUserName          string    `xorm:"comment('收款人姓名') VARCHAR(255)"`
	BillTypeCode          string    `xorm:"comment('结算账户类型') VARCHAR(255)"`
	BillTypeNumber        string    `xorm:"comment('收款账号') VARCHAR(255)"`
	BillBankName          string    `xorm:"comment('开户行') VARCHAR(255)"`
	FreezeTrad            float32   `xorm:"default 0.00 comment('冻结佣金') DECIMAL(12,2)"`
	DistriCode            string    `xorm:"comment('分销代码') VARCHAR(255)"`
	DistriFormid          string    `xorm:"comment('小程序formId') VARCHAR(30)"`
	DistriChainid         int32     `xorm:"default 0 comment('分销门店ID') INT(11)"`
	DistriBrandid         int32     `xorm:"default 0 comment('品牌ID') INT(11)"`
	DistriTime            int32     `xorm:"comment('申请时间') INT(11)"`
	DistriHandleTime      int32     `xorm:"comment('处理时间') INT(11)"`
	DistriShow            int32     `xorm:"not null default 0 comment('分销中心是否显示 0不显示 1显示') TINYINT(1)"`
	QuitTime              int32     `xorm:"comment('退出时间') INT(11)"`
	DistriApplyTimes      int32     `xorm:"default 0 comment('申请次数') INT(11)"`
	DistriQuitTimes       int32     `xorm:"default 0 comment('退出次数') INT(11)"`
	WeixinMpOpenid        string    `xorm:"comment('微信公众号OpenID') VARCHAR(50)"`
	IsCash                int32     `xorm:"default 1 comment('是否允许提现，0否，1是') TINYINT(1)"`
	IdCardName            string    `xorm:"default '' comment('实名认证姓名') VARCHAR(20)"`
	IdCardCode            string    `xorm:"default '' comment('身份证号') VARCHAR(20)"`
	IdCardBind            int32     `xorm:"not null default 0 comment('是否实名认证0否,1是') TINYINT(1)"`
	IdCardState           int32     `xorm:"not null default 0 comment('审核状态0未申请，1待审核，2审核成功，3审核失败') TINYINT(1)"`
	IdCardExplain         string    `xorm:"default '' comment('审核说明') VARCHAR(50)"`
	IdCardImg             string    `xorm:"default '' comment('身份证正反面图片') VARCHAR(50)"`
	WeixinMiniOpenid      string    `xorm:"default '' comment('小程序openid(阿闻宠物-北京那边用)') index VARCHAR(50)"`
	WeixinMiniAddtime     int32     `xorm:"default 0 comment('小程序绑定时间(阿闻宠物-北京那边用)') INT(11)"`
	WeixinMiniOpenidshop  string    `xorm:"default '' comment('小程序openid(阿闻智慧门店-自用)') index VARCHAR(50)"`
	WeixinMiniAddtimeshop int32     `xorm:"default 0 comment('小程序绑定时间(阿闻智慧门店-自用)') INT(11)"`
	WeixinMiniOpenidasq   string    `xorm:"default '' comment('小程序openid(阿闻爱省钱-自用)') index VARCHAR(50)"`
	WeixinMiniAddtimeasq  int32     `xorm:"default 0 comment('小程序绑定时间(阿闻爱省钱-自用)') INT(11)"`
	WeixinMiniAddtimemall int32     `xorm:"default 0 comment('小程序绑定时间(阿闻商城-自用)') INT(11)"`
	EarnestMoney          string    `xorm:"default 0.00 comment('保证金金额') DECIMAL(12,2)"`
	GevalCommentStatus    int32     `xorm:"default 0 comment('0为电商 1为采集 2为宠医云 3阿闻智慧宠物医院4阿闻宠物小程序5阿闻爱省钱6阿闻商城7数据中心8佳雯会员') TINYINT(4)"`
	DisTradMoney          float32   `xorm:"default 0.00 comment('累计收益') DECIMAL(12,2)"`
	BillBankBranch        string    `xorm:"comment('开户银行支行名称') VARCHAR(30)"`
	MemberIsvip           int32     `xorm:"default 0 comment('0.默认1.198会员') TINYINT(1)"`
	MemberIsbzk           int32     `xorm:"default 0 comment('0.默认1.保障卡') TINYINT(1)"`
	MemberVipstime        int32     `xorm:"default 0 comment('会员开始时间') INT(11)"`
	MemberVipetime        int32     `xorm:"default 0 comment('会员过期时间') INT(11)"`
	MemberBzkstime        int32     `xorm:"default 0 comment('保障卡开始时间') INT(11)"`
	MemberBzketime        int32     `xorm:"default 0 comment('保障卡结束时间') INT(11)"`
	UserLevelId           int32     `xorm:"default 0 comment('用户会员等级') INT(11)"`
	UserLevelStime        int64     `xorm:"default 0 comment('用户会员起始时间') INT(11)"`
	UserLevelEtime        int64     `xorm:"default 0 comment('用户会员结束时间') INT(11)"`
	MemberIdentity        string    `xorm:"comment('分销员身份证') VARCHAR(50)"`
	MemberMobileBefore    string    `xorm:"comment('修改前手机号') VARCHAR(11)"`
	MemberVipstarttime    int32     `xorm:"default 0 comment('电商198会员开始时间') INT(11)"`
	MemberVipendtime      int32     `xorm:"default 0 comment('电商198会员结束时间') INT(11)"`
	NewcomerTag           int32     `xorm:"default 0 comment('新用户标记，null未更新、1=新人、2，有可能成为新人、3=老用户') TINYINT(4)"`
	WeixinMiniOpenid2     string    `xorm:"default '' comment('极宠家微信小程序openid') VARCHAR(255) 'weixin_mini_openid2'"`
}

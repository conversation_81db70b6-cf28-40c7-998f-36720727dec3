package integral

import (
	"errors"
	"fmt"
	"github.com/go-xorm/xorm"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"integral-center/models"
	"integral-center/utils"
	"strings"
	"sync"
	"time"
)

type Record struct {
	*models.MemberIntegralRecord `xorm:"extends"`
	BeforeSurplusintegralcount   int    // 之前的记录
	BeforeIntegralreason         string // 之前的原因文本
}

type recomputeHandler struct {
	Db            *xorm.Engine
	ScrmId        string
	Records       []*Record
	Irs           []*Record   // 增加的积分记录
	ExpireDates   []time.Time // 所有过期的时间节点，从大到小
	Logs          []string
	ExpireRecords []*models.MemberIntegralRecord // 所有过期记录
}

// RecomputeByScrmIds 指定scrmId的倒推
func RecomputeByScrmIds(ids []string, recordLog bool) {
	if len(ids) == 0 {
		return
	}

	// 单次插入5000协程
	pageSize := 5000
	start := 0

	for {
		end := start + pageSize
		if end > len(ids) {
			end = len(ids)
		}

		currentIds := ids[start:end]
		info := fmt.Sprintf("积分服务 RecomputeByScrmIds 开始处理%s到%s共%d条", currentIds[0], currentIds[len(currentIds)-1], len(currentIds))
		glog.Info(info + "开始处理")

		wg := new(sync.WaitGroup)
		for _, id := range currentIds {
			wg.Add(1)
			go func(scrmId string) {
				_ = Recompute(scrmId, recordLog)
				wg.Done()
			}(id)
		}

		wg.Wait()
		glog.Info(info + "处理完成")

		// 处理下一页
		start += pageSize

		if len(ids) <= start {
			break
		}
	}
}

// RecomputeByRange 通过id区间反查
func RecomputeByRange(startId int32, endId int32, recordLog bool) {
	db := models.GetDBConn()
	// 每次查30000条
	pageSize := int32(30000)
	start := startId

	for {
		var ids []string
		end := start + pageSize
		// 最后一位没有等于
		if end > endId {
			end = endId + 1
		}

		if err := db.Table("scrm_organization_db.t_scrm_user_info").Select("user_id").
			Where("id >= ? and id < ?", start, end).OrderBy("id asc").Find(&ids); err != nil {
			glog.Info(fmt.Sprintf("积分服务 RecomputeByRange id区间%d-%d，当前%d,查询出错：%s", start, end, start, err.Error()))
			return
		}

		// 没数据也继续查，避免id不连续导致的错误
		if len(ids) > 0 {
			RecomputeByScrmIds(ids, recordLog)
		}

		if end > endId {
			break
		}
		// 跳下一页
		start += pageSize
	}
}

// Recompute 倒推重新计算积分
func Recompute(scrmId string, recordLog bool) (err error) {
	defer func() {
		if err != nil {
			glog.Info("积分服务 Recompute 用户参数：", scrmId, "，出错，", err.Error())
		}
	}()

	h, err := initRecomputeHandler(scrmId)
	if err != nil {
		return
	}

	for _, record := range h.Records {
		// 增加积分
		if record.Integraltype%10 == 1 {
			// 重置过期记录
			if record.Integralreason == "处理过期积分" {
				record.Integralreason = ""
			}
			record.Integralreason = strings.Split(record.Integralreason, ";")[0]
			// 重置剩余积分
			record.Surplusintegralcount = record.Integralcount
			h.Irs = append(h.Irs, record)
			continue
		}

		// 判断过期
		h.expire(record.Createdate)
		// 减扣积分
		h.reduce(record)
	}

	// 以最近的时间重新过期
	h.expire(time.Now())

	if err = h.update(); err != nil {
		return
	}

	// 需要记录执行日志
	if recordLog {
		glog.Info("积分服务 Recompute 用户参数：", scrmId, "，变更记录，", h.Logs)
	}

	return
}

// 初始化处理器
func initRecomputeHandler(scrmId string) (h *recomputeHandler, err error) {
	h = &recomputeHandler{
		ScrmId: scrmId,
		Db:     models.GetDBConn(),
	}

	if err := h.Db.Table("member_integral_record").Where("memberid = ?", scrmId).Where("ischeck = 1 and integraltype <> 82").
		Select("*,surplusintegralcount as before_surplusintegralcount,integralreason as before_integralreason").
		OrderBy("createdate asc").Find(&h.Records); err != nil {
		return nil, errors.New("查询积分记录 " + err.Error())
	}

	// 从2020年7月1号0点开始
	current, _ := time.ParseInLocation(kit.DATE_LAYOUT, "2020-07-01", time.Local)

	for {
		if current.After(time.Now()) {
			break
		}
		h.ExpireDates = append([]time.Time{current}, h.ExpireDates...)
		// 每半年一次
		current = current.AddDate(0, 6, 0)
	}

	return
}

// 减积分
func (h *recomputeHandler) reduce(reduceRecord *Record) {
	remain := reduceRecord.Integralcount
	// 处理减少，优先减少相同订单号的积分
	for _, ir := range h.Irs {
		if ir.Orderid == reduceRecord.Orderid {
			remain = h.singleRecordReduce(ir, reduceRecord, remain)
			break
		}
	}

	// 否则最先的积分开始抵扣
	for _, ir := range h.Irs {
		// 已经足够抵扣了
		if remain < 1 {
			return
		}
		if ir.Surplusintegralcount > 0 {
			remain = h.singleRecordReduce(ir, reduceRecord, remain)
		}
	}
	return
}

// 单条记录减积分，返回还差数量
func (h *recomputeHandler) singleRecordReduce(record *Record, rrd *Record, qty int) (remain int) {
	defer func() {
		var reason string
		if record.Orderid == rrd.Orderid {
			reason = "退货"
		}
		h.Logs = append(h.Logs, fmt.Sprintf("%s因%s%s抵扣了%d积分，剩余%d",
			record.Integralid, rrd.Integralid, reason, qty-remain, record.Surplusintegralcount))
	}()

	// 记录积分足够扣除
	if record.Surplusintegralcount >= qty {
		record.Surplusintegralcount -= qty
		return 0
	}

	remain = qty - record.Surplusintegralcount
	record.Surplusintegralcount = 0
	return
}

// 过期指定时间前的积分
func (h *recomputeHandler) expire(current time.Time) {
	// 最近的过期时间
	var expireDate time.Time

	for _, ed := range h.ExpireDates {
		// 如果当前时间在指定过期时间后，则标记之前的积分全过期
		if current.After(ed) {
			expireDate = ed
			break
		}
	}

	// 不存在过期时间
	if expireDate.IsZero() {
		return
	}

	var expireTotal int

	for _, ir := range h.Irs {
		if ir.Surplusintegralcount < 1 {
			continue
		}
		// 1年半以前的过期
		if ir.Createdate.Before(expireDate.AddDate(-1, -6, 0)) {
			if len(ir.Integralreason) > 0 {
				ir.Integralreason = ir.Integralreason + ";处理过期积分"
			} else {
				ir.Integralreason = "处理过期积分"
			}
			h.Logs = append(h.Logs, fmt.Sprintf("%s在%s过期了%d积分", ir.Integralid, expireDate.Format(kit.DATE_LAYOUT), ir.Surplusintegralcount))
			expireTotal += ir.Surplusintegralcount
			ir.Surplusintegralcount = 0
		}
	}

	if expireTotal > 0 {
		id := utils.GetGuid()
		h.ExpireRecords = append(h.ExpireRecords, &models.MemberIntegralRecord{
			Integralid:           id,
			Orderid:              id,
			Memberid:             h.ScrmId,
			Integralcount:        expireTotal,
			Integraltype:         82,
			Integralreason:       "积分过期",
			Ischeck:              1,
			Createdate:           expireDate,
			Lasttime:             time.Now(),
			Payamount:            0,
			Surplusintegralcount: 0,
		})
	}
}

// 更新积分信息
func (h *recomputeHandler) update() (err error) {
	session := h.Db.NewSession()
	_ = session.Begin()

	var total int

	// 一条增加的都没有，也就不可能有减的
	if len(h.Irs) > 0 {
		for _, ir := range h.Irs {
			total += ir.Surplusintegralcount
			// 数据未变更不处理
			if (ir.BeforeSurplusintegralcount == ir.Surplusintegralcount) && (ir.BeforeIntegralreason == ir.Integralreason) {
				continue
			}
			if _, err = session.Table("member_integral_record").Where("integralid = ?", ir.Integralid).Update(map[string]interface{}{
				"surplusintegralcount": ir.Surplusintegralcount,
				"integralreason":       ir.Integralreason,
			}); err != nil {
				return errors.New("更新积分记录 " + err.Error())
			}
		}

		// 重置过期记录
		if _, err = session.Exec("delete from member_integral_record where memberid = ? and integraltype = 82", h.ScrmId); err != nil {
			return errors.New("删除过期记录 " + err.Error())
		}
		if len(h.ExpireRecords) > 0 {
			if _, err = session.Insert(h.ExpireRecords); err != nil {
				return errors.New("插入过期记录 " + err.Error())
			}
		}
	}

	// 更新用户积分
	if _, err = session.Table("member_integral_info").Where("memberid = ?", h.ScrmId).Update(map[string]interface{}{
		"integral": total,
	}); err != nil {
		return errors.New("更新用户积分 +" + err.Error())
	}

	return session.Commit()
}

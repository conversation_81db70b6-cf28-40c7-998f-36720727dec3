package order

import (
	"github.com/xuri/excelize/v2"
	"testing"
)

func TestExpressImport(t *testing.T) {
	type args struct {
		file   *excelize.File
		userNo string
	}

	file := excelize.NewFile()
	writer, _ := file.NewStreamWriter("Sheet1")
	_ = writer.SetRow("A1", []interface{}{"单号", "快递公司编码", "快递单号"})
	_ = writer.SetRow("A2", []interface{}{"11", "顺丰aaa", "快递单号"})
	_ = writer.SetRow("A3", []interface{}{"11", "顺丰", ""})
	_ = writer.SetRow("A4", []interface{}{"11", "顺丰", "快递单号"})
	_ = writer.SetRow("A5", []interface{}{"112", "快递公司编码", "快递单号"})
	_ = writer.SetRow("A6", []interface{}{"112", "中通", "快递单号"})
	_ = writer.SetRow("A7", []interface{}{"660702149036912064", "", "快递单号"})
	_ = writer.SetRow("A8", []interface{}{"660702149036912064", "中通", "快递单号"})
	_ = writer.Flush()

	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			args: args{file: file, userNo: "23"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := ExpressImport(tt.args.file, "123"); (err != nil) != tt.wantErr {
				t.Errorf("ExpressImport() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

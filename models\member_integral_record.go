package models

import (
	"time"
)

type MemberIntegralRecord struct {
	Integralid           string    `xorm:"not null pk comment('积分记录id') CHAR(50)"`
	Orderid              string    `xorm:"default 'NULL' comment('订单id') CHAR(80)"`
	Memberid             string    `xorm:"default 'NULL' comment('用户id') CHAR(50)"`
	Integralcount        int       `xorm:"default NULL comment('积分变化值') INT(11)"`
	Integraltype         int       `xorm:"default NULL comment('11:咨询服务',12:取消咨询,21:门店消费,22:门店退货,31:预约服务,32:取消预约,41:商城消费,42:商城售后,61:预售商品消,62:预售商品消,52:积分兑换,71:本地生活消费,72:本地生活售后,82积分过期') TINYINT(11)"`
	Integralreason       string    `xorm:"default 'NULL' comment('添加积分原因') VARCHAR(50)"`
	Ischeck              int       `xorm:"default NULL comment('积分反查状态(1--已反查，0--未反查)') TINYINT(1)"`
	Createdate           time.Time `xorm:"default 'current_timestamp()' comment('创建时间') index TIMESTAMP"`
	Lasttime             time.Time `xorm:"not null default 'current_timestamp()' comment('最后操作时间') TIMESTAMP"`
	Payamount            int       `xorm:"default NULL comment('实付金额') INT(10)"`
	Surplusintegralcount int       `xorm:"default NULL comment('剩余积分') INT(11)"`
	OrgId                int       `xorm:"comment('主体ID') INT(11)"`
	UserLevelId          int32     `xorm:"default 0 comment('用户会员等级') INT(11)"`
}

type MemberRecord struct {
	Memberid string `xorm:"default 'NULL' comment('用户id') CHAR(50)"`
	OrgId    int    `xorm:"comment('主体ID') INT(11)"`
}

const (
	//积分抽奖消耗
	IntegralTypeLuckDrawReduce = 92
	//积分兑换爱心币消耗
	IntegralTypeCoin = 102
)

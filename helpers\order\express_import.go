package order

import (
	"errors"
	"fmt"
	"github.com/go-xorm/xorm"
	"github.com/xuri/excelize/v2"
	"integral-center/helpers"
	"integral-center/models"
	"integral-center/utils"
	"strings"
	"time"
)

const (
	inLimit = 5000
	// 最多导入的行数
	maxRows = 10000
	// 导入成功标记
	successRemark = "导入成功"
)

// 导入处理器
type importHandler struct {
	SheetName    string     // 当前工作表名
	Data         [][]string // 文件内容，除表头
	OrderSns     []string   // 所有的订单号
	Extends      map[string]*models.IntegralExchangeExtend
	Writer       *excelize.StreamWriter
	Expresses    []*models.UpetExpress // 所有快递公司
	Task         *models.IntegralExchangeTask
	SuccessCount int32 // 成功计数
	FailCount    int32 //失败计数
}

// ExpressImport 物流导入
func ExpressImport(file *excelize.File, userNo string) (err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("导入异常 %s", e)
		}
	}()

	r := utils.ConnectRedis()
	defer r.Close()
	lockKey := "integral-center:order:express-import"
	if lock, err := r.SetNX(lockKey, 1, time.Second*60).Result(); err != nil {
		return errors.New("获取锁失败 " + err.Error())
	} else if !lock {
		return errors.New("有正在执行的导入任务，请稍后重试")
	}
	defer r.Del(lockKey)

	h, err := initImportHandler(file, userNo)
	if err != nil {
		return
	}

	session := models.GetDBConn().NewSession()
	defer session.Close()
	_ = session.Begin()

	h.batchDelivery(session)

	if err = h.Writer.Flush(); err != nil {
		_ = session.Rollback()
		return
	}
	// 上传excel文件
	if h.Task.Url, err = helpers.UploadExcelToQiNiu(file, ""); err != nil {
		_ = session.Rollback()
		return
	}
	// 插入导入记录
	if _, err = session.Table("dc_order.integral_exchange_task").Insert(h.Task); err != nil {
		_ = session.Rollback()
		return
	}

	if err = session.Commit(); err != nil {
		return errors.New("提交事务出错 " + err.Error())
	}

	return
}

// 初始化导入处理器
func initImportHandler(file *excelize.File, userNo string) (h *importHandler, err error) {
	h = new(importHandler)

	if err = h.readFile(file); err != nil {
		return
	} else if len(h.Data) == 0 {
		return nil, errors.New("上传的文件不存在数据")
	}

	// 分段查询订单信息
	if err = h.sliceQueryExtends(); err != nil {
		return
	}

	if err = models.GetUpetDBConn().Table("upet_express").Select("e_name,e_code_kdniao").Where("e_state = '1'").
		OrderBy("e_order asc,e_letter asc").Find(&h.Expresses); err != nil {
		err = errors.New("查询快递公司出错 " + err.Error())
		return
	}

	// 初始化任务
	h.Task = &models.IntegralExchangeTask{
		Type:   1,
		State:  models.IETaskStateSuccess,
		UserNo: userNo,
	}

	// 初始化excel写入器
	h.Writer, _ = file.NewStreamWriter(h.SheetName)
	_ = h.Writer.SetRow("A1", []interface{}{
		"单号", "快递公司编码", "快递单号", "导入结果",
	})

	return
}

// 获取excel文件内容
func (h *importHandler) readFile(file *excelize.File) (err error) {
	h.SheetName = file.GetSheetName(0)

	rows, err := file.Rows(h.SheetName)
	if err != nil {
		return errors.New("获取行数据出错 " + err.Error())
	}

	for i := 0; rows.Next(); i++ {
		// 注意这里一定要读取行，不然内容会附加到下一行
		row, err := rows.Columns()
		// 表头不处理
		if i == 0 {
			continue
		}
		if i > maxRows {
			return fmt.Errorf("最多导入%v万行数据", maxRows)
		}
		if err != nil {
			return errors.New("获取列数据出错 " + err.Error())
		}
		for k, v := range row {
			row[k] = strings.TrimSpace(v)
		}

		// 无效的空数忽略
		if len(row) == 0 || len(row[0]) < 1 {
			continue
		}
		// 补全数据
		row = append(row, "", "")[0:3]

		h.Data = append(h.Data, row)
		h.OrderSns = append(h.OrderSns, row[0])
	}

	_ = rows.Close()
	return
}

// 分段查询订单
func (h *importHandler) sliceQueryExtends() (err error) {
	db := models.GetDBConn()
	total := len(h.OrderSns)
	h.Extends = make(map[string]*models.IntegralExchangeExtend)

	// 分段查询
	for i := 0; i < total; i = i + inLimit {
		end := i + inLimit
		if end > total {
			end = total
		}
		in := h.OrderSns[i:end]

		var t []*models.IntegralExchangeExtend
		if err := db.Table("dc_order.integral_exchange").Alias("e").
			Join("left", "dc_order.integral_exchange_address a", "a.exchange_id = e.id").
			In("e.order_sn", in).OrderBy("e.id asc").Find(&t); err != nil {
			return errors.New("查订单出错 " + err.Error())
		}
		for _, s := range t {
			h.Extends[s.OrderSn] = s
		}
	}

	return
}

// 处理发货
func (h *importHandler) batchDelivery(session *xorm.Session) {
	// 导入成功的记录，用于避免重复的导入
	successes := make(map[string]bool)

	// []string 单号、快递公司编码、快递单号
	for i, data := range h.Data {
		r := []interface{}{data[0], data[1], data[2], successRemark}
		if successes[data[0]] {
			r[3] = "重复导入"
			h.FailCount++
		} else if err := h.singleDelivery(session, h.Extends[data[0]], data); err != nil {
			r[3] = err.Error()
			h.FailCount++
		} else {
			successes[data[0]] = true
			h.SuccessCount++
		}
		_ = h.Writer.SetRow(fmt.Sprintf("A%d", i+2), r)
	}

	if h.SuccessCount > 0 {
		if h.FailCount > 0 {
			h.Task.Result = fmt.Sprintf("成功%v，失败%v", h.SuccessCount, h.FailCount)
		} else {
			h.Task.Result = fmt.Sprintf("全部成功%v", h.SuccessCount)
		}
	} else {
		h.Task.Result = fmt.Sprintf("全部失败%v", h.FailCount)
	}
}

// 单个处理发货
func (h *importHandler) singleDelivery(session *xorm.Session, extend *models.IntegralExchangeExtend, data []string) error {
	if extend == nil {
		return errors.New("订单号无效")
	} else if extend.Type != models.IETypeGoods {
		return errors.New("非实物订单")
	} else if extend.State != models.IEStateDelivery {
		return errors.New("非待发货订单")
	} else if len(data[2]) == 0 {
		return errors.New("快递单号不能为空")
	} else if len(data[1]) == 0 { // 快递公司不校验空值
		return errors.New("快递公司编码不能为空")
	}

	address := &models.IntegralExchangeAddress{
		ShippingTime: int32(time.Now().Unix()),
		ShippingCode: data[2],
	}

	// 存在快递公司
	if len(data[1]) > 1 {
		if express := h.getExpressByName(data[1]); express != nil {
			address.ShippingEcode = express.ECodeKdniao
			address.ShippingEname = express.EName
		} else {
			return errors.New("快递公司编码无效")
		}
	}

	return extend.Delivery(session, address)
}

// 获取快递公司数据
func (h *importHandler) getExpressByName(name string) *models.UpetExpress {
	for _, express := range h.Expresses {
		// 公司名称包含或者等于编码
		if strings.Contains(express.EName, name) || express.ECodeKdniao == strings.ToUpper(name) {
			return express
		}
	}
	return nil
}

package main

import (
	"flag"
	"integral-center/proto/igc"
	"integral-center/proto/oc"
	"integral-center/services"
	"integral-center/tasks"
	_ "integral-center/tasks"
	"time"

	"github.com/limitedlee/microservice/micro"
	"github.com/maybgit/glog"
	"google.golang.org/grpc/reflection"
)

func init() {
	sh, _ := time.LoadLocation("Asia/Shanghai")
	time.Local = sh
}

func main() {
	defer glog.Flush()
	flag.Parse()
	micro := micro.MicService{}
	micro.NewServer()
	//创建数据库连接
	services.SetupDbConn()
	defer services.CloseDbConn()
	oc.RegisterIntegralServiceServer(micro.GrpcServer, &services.IntegralService{})

	igc.RegisterIntegralServiceServer(micro.GrpcServer, &services.IntegralService{})
	igc.RegisterIntegralGoodsServiceServer(micro.GrpcServer, &services.GoodsService{})
	igc.RegisterIntegralOrderServiceServer(micro.GrpcServer, &services.OrderService{})
	igc.RegisterTaskChooseServer(micro.GrpcServer, &tasks.TaskChoose{})

	//服务反射，便于查看grpc的状态
	reflection.Register(micro.GrpcServer)
	glog.Info("integral-center v4.2.3 服务启动...")
	micro.Start()
}

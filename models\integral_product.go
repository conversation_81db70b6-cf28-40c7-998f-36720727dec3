package models

import (
	"integral-center/helpers"
	"integral-center/proto/igc"
	"strings"
	"time"

	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

type IntegralProduct struct {
	Id            int32     `xorm:"not null pk comment('积分礼品索引id') INT(11)"`
	Name          string    `xorm:"default '' comment('积分礼品名称') VARCHAR(40)"`
	Price         string    `xorm:"default 0.00 comment('积分礼品原价') DECIMAL(10,2)"`
	Points        int32     `xorm:"default 0 comment('积分礼品兑换所需积分') INT(11)"`
	Image         string    `xorm:"default '' comment('积分礼品默认封面图片') VARCHAR(100)"`
	Serial        string    `xorm:"default '' comment('积分礼品货号') VARCHAR(50)"`
	Storage       int32     `xorm:"default 0 comment('积分礼品库存数') INT(11)"`
	SaleNum       int32     `xorm:"default 0 comment('积分礼品售出数量') INT(11)"`
	View          int32     `xorm:"default 0 comment('积分商品浏览次数') INT(11)"`
	IsLimit       int32     `xorm:"default 0 comment('是否限制每会员兑换数量') TINYINT(1)"`
	LimitNum      int32     `xorm:"default 0 comment('每会员限制兑换数量')  INT(11)"`
	LimitMgrade   int32     `xorm:"default 0 comment('限制参与兑换的会员级别1限制0不限制')  INT(11)"`
	Show          int32     `xorm:"default 0 comment('积分礼品上架 0表示下架 1表示上架') TINYINT(1)"`
	Commend       int32     `xorm:"default 0 comment('积分礼品推荐0不推荐 1推荐') TINYINT(1)"`
	GoodType      int32     `xorm:"default 1 comment('商品类型 1第三方商品 2自有商品3门店卷') TINYINT(1)"`
	IsLimitTime   int32     `xorm:"default 0 comment('是否限制兑换时间 0为不限制 1为限制）') TINYINT(1)"`
	Type          int32     `xorm:"default 1 comment('类型1实物2虚拟3优惠券') TINYINT(1)"`
	ScrmInfo      string    `xorm:"default 'NULL' comment('虚拟商品sku或scrm优惠券ID') VARCHAR(50)"`
	ScrmInfoState int32     `xorm:"default 1 comment('scrm_info关联状态 0下架 1上架') INT(11)"`
	Body          string    `xorm:"default '' comment('积分礼品详细内容') TEXT"`
	CreateTime    time.Time `xorm:"default 'current_timestamp()' comment('积分礼品添加时间') DATETIME created"`
	StartTime     time.Time `xorm:"default NULL comment('兑换开始时间') DATETIME"`
	EndTime       time.Time `xorm:"default NULL comment('兑换结束时间') DATETIME"`
	UserLevelIds  string    `xorm:"default '' comment('会员等级，多个等级使用逗号分割') varchar(20) 'user_level_ids'"`
	CoinNum       int32     `xorm:"comment('爱心币的兑换数量') INT(11)" json:"coin_num"`
	StoreId       int32     `xorm:"not null  comment('店铺id') INT(11) <-"` //电商主体id
}

func (p IntegralProduct) ToIgcGoodsDetail() *igc.GoodsDetail {
	g := &igc.GoodsDetail{
		Id:          p.Id,
		Name:        p.Name,
		Price:       cast.ToFloat32(p.Price),
		Points:      p.Points,
		Image:       helpers.GetIntegralProductUrl(p.Image, false),
		Serial:      p.Serial,
		Show:        p.Show,
		Commend:     p.Commend,
		Body:        p.Body,
		IsLimit:     p.IsLimit,
		LimitNum:    p.LimitNum,
		IsLimitTime: p.IsLimitTime,
		LimitMgrade: p.LimitMgrade,
		Type:        p.Type,
		ScrmInfo:    cast.ToInt64(p.ScrmInfo),
		GoodType:    p.GoodType,
		SaleNum:     p.SaleNum,
		Storage:     p.Storage,
		CoinNum:     p.CoinNum,
		OrgId:       p.StoreId,
	}
	var ids = make([]int32, 0)
	if len(p.UserLevelIds) > 0 {
		split := strings.Split(p.UserLevelIds, ",")
		for i := range split {
			ids = append(ids, cast.ToInt32(split[i]))
		}
	}
	g.UserLevelIds = ids // 复制vip

	if p.StartTime.IsZero() {
		g.StartTime = ""
	} else {
		g.StartTime = p.StartTime.Format(kit.DATETIME_LAYOUT)
	}
	if p.EndTime.IsZero() {
		g.EndTime = ""
	} else {
		g.EndTime = p.EndTime.Format(kit.DATETIME_LAYOUT)
	}
	return g
}

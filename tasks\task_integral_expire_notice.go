package tasks

import (
	"fmt"
	"github.com/spf13/cast"
	"integral-center/models"
	"integral-center/proto/cc"
	"integral-center/proto/mc"
	"integral-center/utils"
	"time"

	"github.com/maybgit/glog"
	"github.com/ppkg/kit"
)

// 订阅消息模板
var integralExpireSubscribeMessageTemplate *models.WechatSubscribeMessageTemplate

// 积分到期提醒
func taskIntegralExpireNotice() {

	//初始化redis
	redisConn := utils.GetRedisConnLong()

	taskLock := "integralcenter:task:taskIntegralExpireNotice"
	if !redisConn.SetNX(taskLock, time.Now().Unix(), 12*time.Hour).Val() {
		glog.Error("taskIntegralExpireNotice already running")
		return
	}

	defer func() {
		// 删除key
		redisConn = utils.GetRedisConnLong()
		redisConn.Del(taskLock)
	}()
	glog.Info("taskIntegralExpireNotice start")
	// 积分过期日期是7月1号或者1月1号，定时任务提前7天执行，这里判断是否6月24、12月24
	_, _, day := time.Now().Date()
	doDay := redisConn.Get("integral:ExpireNotice:doDay").Val()
	if doDay == "" {
		doDay = "24"
	}
	if cast.ToString(day) != doDay {
		return
	}

	//检测当天过期提醒是否已经处理过了
	nowTimeStr := time.Now().Format(kit.DATE_LAYOUT)
	//去redis读取是否已经执行过
	lastDate := redisConn.Get("integral:ExpireNotice:date").Val()
	//已经处理过了跳出
	if nowTimeStr == lastDate {
		return
	}

	// 积分过期日期是6月30号或者12月31号
	// 一年半以前并且剩余积分大于0的增加积分的流水需要过期
	//判断时间 - 获取开始时间和结束时间
	monthNum := time.Now().Month()
	yearNum := time.Now().Year() - 2

	//开始时间，结束时间
	var expireTime, endDate string

	expireTime = time.Date(yearNum, monthNum+1, 1, 0, 0, 0, 0, time.Now().Location()).Format(kit.DATE_LAYOUT)
	endDate = time.Date(yearNum, monthNum+2, 1, 0, 0, 0, 0, time.Now().Location()).Format(kit.DATE_LAYOUT)
	page, size := 1, 100
	for {
		list := make([]*models.MemberIntegralRecord, 0)
		models.GetDBConn().ShowSQL()

		err := models.GetDBConn().Table("member_integral_record").Select("memberid,org_id").
			Where("ischeck = 1 AND integralcount > 0 and surplusintegralcount >0 AND MOD(integraltype,2) = 1 AND createdate < ? and createdate>=?", endDate, expireTime).
			Limit(size, (page-1)*size).GroupBy("memberid,org_id").Find(&list)
		if err != nil {
			glog.Error("taskIntegralExpireNotice error: ", err)
			return
		}
		if len(list) == 0 {
			break
		}

		for _, v := range list {
			handleUserIntegralRecords(v.Memberid, expireTime, endDate, v.OrgId)
		}
		time.Sleep(200 * time.Millisecond)

		page++
	}
	redisConn.Set("integral:ExpireNotice:date", nowTimeStr, 0)
	glog.Info("taskIntegralExpireNotice end.")
}

func handleUserIntegralRecords(memberId, expireTime, endData string, orgId int) {
	records := make([]*models.MemberIntegralRecord, 0)
	err := models.GetDBConn().Table("member_integral_record").Select("integralid,memberid,surplusintegralcount").
		Where("ischeck = 1 AND integralcount > 0 and surplusintegralcount >0 AND memberid = ? AND MOD(integraltype,2) = 1 AND createdate < ? and createdate>=? and org_id=?", memberId, endData, expireTime, orgId).Find(&records)
	if err != nil {
		glog.Error("handleUserIntegralRecords error: ", err)
		return
	}
	//nowIntegral := 0
	info := models.MemberIntegralInfo{}
	_, err = models.GetDBConn().Table("member_integral_info").Where(" memberid = ? and org_id=?", memberId, orgId).Get(&info)
	if err != nil {
		glog.Error("handleUserIntegralRecords error: ", err)
		return
	}
	//查询用户当前积分

	userExpireIntegral := 0 //用户需要过期的积分数
	for _, record := range records {
		userExpireIntegral += record.Surplusintegralcount
	}

	if userExpireIntegral > 0 {
		sendIntegralExpireMsg(memberId, expireTime, userExpireIntegral, orgId, info.Integral)
	}
}

// 积分变更通知
func sendIntegralExpireMsg(memberId, expireTime string, expireIntegral int, orgId int, nowIntegral int) {
	subTemplate, err := getIntegralExpireSubscribeMessageTemplate(orgId)
	if err != nil {
		glog.Error("sendIntegralExpireMsg getIntegralExpireSubscribeMessageTemplate error: ", err, memberId)
		return
	}

	member := &models.UpetMember{}
	_, err = models.GetUpetDBConn().Table("upet_member").Where("scrm_user_id=?", memberId).Get(member)
	if err != nil {
		glog.Error("sendIntegralExpireMsg upet_member error: ", err, memberId)
		return
	}

	miniOpenId := member.WeixinMiniOpenid
	if orgId == 2 {
		miniOpenId = member.WeixinMiniOpenid2
	} else if orgId == 3 {
		miniOpenId, err = getEshopUser(cast.ToInt(memberId), 3)
		if err != nil {
			glog.Error("sendIntegralExpireMsg 获取用户福码购小程序的openid异常 error: ", err, memberId)
			return
		}
	}

	// 是否能推送订阅消息
	ccClient := cc.GetCustomerCenterClient()
	defer ccClient.Close()
	canRe, err := ccClient.RPC.CanSendWechatSubscribeMessage(ccClient.Ctx, &cc.CanSendWechatSubscribeMessageReq{
		ScrmUserId:    member.ScrmUserId,
		SubscribeType: "integral",
		TemplateId:    subTemplate.TemplateId,
	})
	if err != nil {
		glog.Error("sendIntegralExpireMsg CanSendWechatSubscribeMessage err: ", err.Error())
		return
	}
	if canRe.Code != 200 {
		glog.Info("sendIntegralExpireMsg CanSendWechatSubscribeMessage ", memberId, kit.JsonEncode(canRe))
		return
	}

	msgClient := mc.GetMessageCenterClient()
	miniprogramState := ""
	if kit.EnvIsUAT() {
		miniprogramState = "trial"
	}
	ftime, _ := time.ParseInLocation("2006-01-02", expireTime, time.Local)
	ftime = ftime.AddDate(2, 0, 0)
	formatExpireTime := ftime.Format("2006年01") // 积分过期时间
	mes := fmt.Sprintf("您有%d积分将于%s月到期", expireIntegral, formatExpireTime)
	re, err := msgClient.RPC.SubscribeMessage(msgClient.Ctx, &mc.SubscribeMessageRequest{
		Touser:           miniOpenId,
		TemplateId:       subTemplate.TemplateId,
		Page:             "/app/points/center/my", // 小程序跳转页面
		MiniprogramState: miniprogramState,
		Data:             fmt.Sprintf(subTemplate.Content, nowIntegral, mes),
		IsJpush:          0,
		OrgId:            cast.ToInt32(orgId),
	})
	if err != nil {
		glog.Error("sendIntegralExpireMsg error: ", err, memberId)
		return
	}
	if re.Code != 200 {
		glog.Error("sendIntegralExpireMsg 发送积分变更通知失败，", memberId, kit.JsonEncode(re))
		return
	}

	// 订阅数减一
	_, err = models.GetDBConn().Exec("UPDATE wechat_subscribe_message SET number=IF(number<1,0,number-1) WHERE scrm_user_id=? AND template_id=?", member.ScrmUserId, subTemplate.TemplateId)
	if err != nil {
		glog.Error("sendIntegralExpireMsg 扣减用户订阅数失败，error: ", err, memberId)
		return
	}
}

func getEshopUser(memberId int, orgId int) (string, error) {
	var miniOpenId string
	_, err := models.GetDBConn().SQL("SELECT weixin_mini_openid FROM eshop.users Where member_id=? AND org_id=?", memberId, orgId).Get(&miniOpenId)
	if err != nil {
		glog.Error("获取福码购小程序的用户openId失败")
		return "", err
	}
	return miniOpenId, nil
}

func getIntegralExpireSubscribeMessageTemplate(orgId int) (*models.WechatSubscribeMessageTemplate, error) {
	// 获取订阅消息模板
	if integralExpireSubscribeMessageTemplate != nil {
		return integralExpireSubscribeMessageTemplate, nil
	}

	integralExpireSubscribeMessageTemplate = &models.WechatSubscribeMessageTemplate{}
	_, err := models.GetDBConn().Table("wechat_subscribe_message_template").Where("template_key=? AND store_id=?", "user-integral-expire", orgId).Get(integralExpireSubscribeMessageTemplate)
	if err != nil {
		return nil, err
	}
	return integralExpireSubscribeMessageTemplate, nil
}

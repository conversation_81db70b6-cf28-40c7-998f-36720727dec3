package models

import "time"

// 微信订阅消息通知开关
type WechatSubscribeNotification struct {
	Id         int64     `json:"id" xorm:"pk autoincr not null INT(11) 'id'"`
	ScrmUserId string    `json:"scrm_user_id" xorm:"not null default '' comment('用户ID作为唯一标识') VARCHAR(32) 'scrm_user_id'"`
	All        int64     `json:"all" xorm:"not null default 0 comment('所有通知总开关，1开启、0关闭') TINYINT(4) 'all'"`
	UserLevel  int64     `json:"user_level" xorm:"not null default 0 comment('用户会员等级通知，1开启、0关闭') TINYINT(4) 'user_level'"`
	Integral   int64     `json:"integral" xorm:"not null default 0 comment('积分通知，1开启、0关闭') TINYINT(4) 'integral'"`
	Voucher    int64     `json:"voucher" xorm:"not null default 0 comment('优惠券通知，1开启、0关闭') TINYINT(4) 'voucher'"`
	CreateTime time.Time `json:"create_time" xorm:"created 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`
	UpdateTime time.Time `json:"update_time" xorm:"updated 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time'"`
}

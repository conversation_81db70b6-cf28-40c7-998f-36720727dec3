package tasks

import (
	"integral-center/models"
	"time"

	"github.com/maybgit/glog"
	"github.com/robfig/cron/v3"
	kit "github.com/tricobbler/rp-kit"
)

func init() {
	if !kit.EnvCanCron() {
		return
	}
	c := cron.New()
	// 每日0点更新积分兑换订单过期状态
	if _, err := c.AddFunc("@daily", updateOrderExpire); err != nil {
		glog.Info("积分商城 updateOrderExpire 创建任务出错：", err.Error())
	}

	// 更新进行中的任务为超时
	if _, err := c.AddFunc("@hourly", updateTaskTimeout); err != nil {
		glog.Info("积分商城 updateTaskTimeout 创建任务出错：", err.Error())
	}

	// 积分到期提醒，每个月24号提醒下个月应该要过期多少气氛
	if _, err := c.AddFunc("@hourly", taskIntegralExpireNotice); err != nil {
		glog.Info("积分到期提醒 updateTaskTimeout 创建任务出错：", err.Error())
	}

	// 校验
	c.Start()
}

// 更新积分兑换订单过期
func updateOrderExpire() {
	_, err := models.GetDBConn().Table("dc_order.integral_exchange").
		Where("state = ?", models.IEStateUse). // 待使用的标记为过期
		Where("type in (2,3) and coupon_end_time > 0 and coupon_end_time < ?", time.Now().Unix()).
		Update(map[string]interface{}{
			"state": models.IEStateExpired,
		})
	if err != nil {
		glog.Info("积分商城 updateOrderExpire 出错：", err.Error())
	}
}

// 更新任务状态为超时
func updateTaskTimeout() {
	_, err := models.GetDBConn().Table("dc_order.integral_exchange_task").
		Where("state = ?", models.IETaskStateIng).
		Where("created_at < ?", time.Now().Add(-time.Hour).Format(kit.DATETIME_LAYOUT)).
		Update(map[string]interface{}{
			"state":  models.IETaskStateFail,
			"result": "处理超时",
		})
	if err != nil {
		glog.Info("积分商城 updateTaskTimeout 出错：", err.Error())
	}
}

package dto

//订单同步 emall.order.synchronize
type Orders struct {
	Orders []OrderParam `json:"orders"`
}

type ReOrders struct {
	Orders  []ReOrdersDet `json:"orders"`
	Code    int32         `json:"code"`
	Message string        `json:"message"`
}

type ReOrdersDet struct {
	Tid      string `json:"tid"`
	Billcode string `json:"billcode"`
	Message  string `json:"message"`
	Status   string `json:"status"`
}

type OrderParam struct {
	//网店订单编号
	Tid string `json:"tid,omitempty"`
	//重量
	Weight string `json:"weight,omitempty"`
	//尺寸
	Size string `json:"size,omitempty"`
	//买家账号
	Buyernick string `json:"buyernick,omitempty"`
	//卖家留言
	Buyermessage string `json:"buyermessage,omitempty"`
	//卖家备注 可以用于设置对应仓库关系
	Sellermemo string `json:"sellermemo,omitempty"`
	//订单总金额
	Total string `json:"total,omitempty"`
	//订单享受优惠的金额
	Privilege string `json:"privilege,omitempty"`
	//运费
	Postfee string `json:"postfee,omitempty"`
	//收货人名称
	Receivername string `json:"receivername,omitempty"`
	//收货省
	Receiverstate string `json:"receiverstate,omitempty"`
	//收货市
	Receivercity string `json:"receivercity,omitempty"`
	//收货区
	Receiverdistrict string `json:"receiverdistrict,omitempty"`
	//收货地址
	Receiveraddress string `json:"receiveraddress,omitempty"`
	//收货电话
	Receiverphone string `json:"receiverphone,omitempty"`
	//收货人手机号
	Receivermobile string `json:"receivermobile,omitempty"`
	//
	Receiverzip string `json:"receiverzip,omitempty"`
	//订单创建时间
	Created string `json:"created,omitempty"`
	//订单状态 NoPay = 未付款 Payed = 已付款 Sended = 已发货 TradeSuccess = 交易成功 TradeClosed = 交易关闭 PartSend = 部分发货
	Status string `json:"status,omitempty"`
	//订单类型（Cod=货到付款, NoCod=非货到付款）
	Type string `json:"type,omitempty"`
	//发票抬头
	Invoicename string `json:"invoicename,omitempty"`
	//卖家旗帜（数值型）
	Sellerflag string `json:"sellerflag,omitempty"`
	//付款时间（时间格式：yyyy-MM-dd HH:mm:ss）
	Paytime string `json:"paytime,omitempty"`
	//物流公司编码
	Logistbtypecode []string `json:"logistbtypecode,omitempty"`
	//往来单位编码
	Btypecode string `json:"btypecode,omitempty"`
	//订单商品信息
	Details []OrderDetailsParam `json:"details,omitempty"`
}

//订单详情信息
type OrderDetailsParam struct {
	//网店订单明细编号
	Oid string `json:"oid,omitempty"`
	//商品条码
	Barcode string `json:"barcode,omitempty"`
	//网店商品ID
	Eshopgoodsid string `json:"eshopgoodsid,omitempty"`
	//网店商家编码
	Outeriid string `json:"outeriid,omitempty"`
	//网店商品名称
	Eshopgoodsname string `json:"eshopgoodsname,omitempty"`
	//网店商品SKU ID
	Eshopskuid string `json:"eshopskuid,omitempty"`
	//网店商品SKU名称
	Eshopskuname string `json:"eshopskuname,omitempty"`
	//商品ID
	Numiid string `json:"numiid,omitempty"`
	//规格ID
	Skuid string `json:"skuid,omitempty"`
	//基本单位数量（不能为0）
	Num string `json:"num,omitempty"`
	//商品总额
	Payment string `json:"payment,omitempty"`
	//商品图片路径
	Picpath string `json:"picpath,omitempty"`
	//重量
	Weight string `json:"weight,omitempty"`
	//尺寸，体积
	Size string `json:"size,omitempty"`
	//销售单位ID
	Unitid string `json:"unitid,omitempty"`
	//销售单位数量
	Unitqty string `json:"unitqty,omitempty"`
}
type TonkPar struct {
	Appkey    string `json:"appkey ,omitempty"`
	Appsecret string `json:"appsecret ,omitempty"`
	Oauthcode string `json:"oauthcode,omitempty"`
}

//emall.afterorder.synchronize  售后订单同步
type AfterorderOrder struct {
	Orders []AfterorderOrderInfo `json:"orders"`
}

type AfterorderOrderInfo struct {
	Rtid           string                       `json:"rtid,omitempty"`
	Tid            string                       `json:"tid,omitempty"`
	Total          string                       `json:"total,omitempty"`
	Privilege      string                       `json:"privilege,omitempty"`
	Postfee        string                       `json:"postfee,omitempty"`
	Created        string                       `json:"created,omitempty"`
	Status         string                       `json:"status,omitempty"`
	Aftsaletype    string                       `json:"aftsaletype,omitempty"`
	Reasoncode     string                       `json:"reasoncode,omitempty"`
	Logistbillcode string                       `json:"logistbillcode,omitempty"`
	Aftsaleremark  string                       `json:"aftsaleremark,omitempty"`
	Details        []AfterorderOrderInfoDetails `json:"details,omitempty"`
}

type AfterorderOrderInfoDetails struct {
	Oid            string `json:"oid,omitempty"`
	Eshopgoodsname string `json:"eshopgoodsname,omitempty"`
	Eshopskuname   string `json:"eshopskuname,omitempty"`
	Backqty        string `json:"backqty,omitempty"`
	Backtotal      string `json:"backtotal,omitempty"`
	Outeriid       string `json:"outeriid,omitempty"`
}

//TOKEN返回JSON
type TokenInfo struct {
	Code            int    `json:"code,"`
	Message         string `json:"message,"`
	Token           string `json:"token,"`
	Expiredate      string `json:"expiredate,"`
	Refresh_token   string `json:"refresh_token,"`
	Appkey          string `json:"appkey,"`
	Appsecret       string `json:"appsecret,"`
	Selfmallaccount string `json:"selfmallaccount,"`
}

//selfmall.order.send  订单发货
type OrderSend struct {
	Tid         string `json:"tid,omitempty"`
	Companycode string `json:"companycode,omitempty"`
	Issplit     int32  `json:"issplit,omitempty"`
	Outsid      string `json:"outsid,omitempty"`
	Subtid      string `json:"subtid,omitempty"`
}
type OrderSendRetInfo struct {
	Tid      string `json:"tid,omitempty"`
	Issplit  string `json:"issplit,omitempty"`
	Code     int32  `json:"code,omitempty"`
	Message  string `json:"message,omitempty"`
	Iserror  bool   `json:"iserror,omitempty"`
	Errormsg string `json:"errormsg,omitempty"`
	Subtid   string `json:"subtid,omitempty"`
}

type AfterorderStatusRetInfo struct {
	Code     int32  `json:"code,omitempty"`
	Message  string `json:"message,omitempty"`
	Iserror  bool   `json:"iserror,omitempty"`
	Errormsg string `json:"errormsg,omitempty"`
}

//selfmall.afterorder.status.sync  售后单状态回写
type AfterorderStatusSync struct {
	Tid    string `json:"tid,omitempty"`
	Status string `json:"status,omitempty"`
}

//emall.orderstatus.synchronize  订单状态同步
type OrderStatusSynchronize struct {
	Orders []OrderStatusInfo `json:"orders"`
}

type OrderStatusInfo struct {
	Tid          string `json:"tid,omitempty"`
	Status       string `json:"status,omitempty"`
	Refundstatus string `json:"refundstatus,omitempty"`
}

//售后订单同步  响应参数
type RetAfterorder struct {
	Orders  []RetAfterorderInfo `json:"orders,omitempty"`
	Code    int                 `json:"code,omitempty"`
	Message string              `json:"message,omitempty"`
}

type RetAfterorderInfo struct {
	Iserror  bool   `json:"iserror,omitempty"`
	Tid      string `json:"tid,omitempty"`
	Billcode string `json:"billcode,omitempty"`
	Message  string `json:"message,omitempty"`
}

//商品SKU实体
type GoodsSku struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Error   string `json:"error"`
	Details []struct {
		ID          int `json:"id"`
		ProductID   int `json:"product_id"`
		MarketPrice int `json:"market_price"`
		RetailPrice int `json:"retail_price"`
		SkuValue    []struct {
			ID             int         `json:"id"`
			SpecID         int         `json:"spec_id"`
			SpecValueID    int         `json:"spec_value_id"`
			SkuID          int         `json:"sku_id"`
			ProductID      int         `json:"product_id"`
			Pic            string      `json:"pic"`
			SpecName       string      `json:"spec_name"`
			SpecValueValue string      `json:"spec_value_value"`
			Details        interface{} `json:"details"`
		} `json:"sku_value"`
		SkuThird []struct {
			ID         int    `json:"id"`
			SkuID      int    `json:"sku_id"`
			ThirdSkuID string `json:"third_sku_id"`
			ErpID      int    `json:"erp_id"`
			ProductID  int    `json:"product_id"`
			ErpName    string `json:"erp_name"`
			ThirdSpuID string `json:"third_spu_id"`
		} `json:"sku_third"`
	} `json:"details"`
}

//获取仓库返回结构体
type RetStock struct {
	Total  int `json:"total"`
	Stocks []struct {
		Goodsid   string  `json:"goodsid"`
		Goodscode string  `json:"goodscode"`
		Goodsname string  `json:"goodsname"`
		Skuid     string  `json:"skuid"`
		Skucode   string  `json:"skucode"`
		Skuname   string  `json:"skuname"`
		Whsname   string  `json:"whsname"`
		Whscode   string  `json:"whscode"`
		Qty       float64 `json:"qty"`
		Enablenum string  `json:"enablenum"`
		Ensalenum string  `json:"ensalenum"`
	} `json:"stocks"`
	Code    int    `json:"code"`
	Message string `json:"message"`
}

//全渠道获取库存请求参数
type StockGetRequest struct {
	Page int `json:"page"`
	Pagesize int `json:"pagesize"`
	Goodscode string `json:"goodscode"`
	Skucode string `json:"skucode"`
	Whscode string `json:"whscode"`
	Iscontainwhs bool `json:"iscontainwhs"`
}
//全渠道获取库存返回
type StockGetResponse struct {
	Code int `json:"code"`
	Message string `json:"message"`
	Total int `json:"total"`
	Stocks []StocksDetil `json:"stocks"`
}
type StocksDetil struct {
	Goodsid string `json:"goodsid"`
	Goodscode string `json:"goodscode"`
	Goodsname string `json:"goodsname"`
	Skuid string `json:"skuid"`
	Skuname string `json:"skuname"`
	Qty float64 `json:"qty"`
	Enablenum float64 `json:"enablenum"`
	Ensalenum float64 `json:"ensalenum"`
	Whscode string `json:"whscode"`
	Whsname string `json:"whsname"`
}

type GoodsStock struct {
	GoodsId string
	Stock   int
}
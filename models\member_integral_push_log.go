package models

import (
	"time"
)

type MemberIntegralPushLog struct {
	Id           int       `xorm:"not null pk autoincr comment('主键') INT(11)"`
	OrderSn      string    `xorm:"not null default ''""'' comment('父订单号') VARCHAR(50)"`
	IntegralType int       `xorm:"not null default 0 comment('积分类型: 41消费增加积分;42退货扣减积分') INT(11)"`
	RefundSn     string    `xorm:"not null default ''""'' comment('退款订单') VARCHAR(50)"`
	MqInfo       string    `xorm:"not null default ''""'' comment('mq信息') VARCHAR(2000)"`
	ErrInfo      string    `xorm:"not null default ''""'' comment('错误信息') VARCHAR(2000)"`
	BeijingInfo  string    `xorm:"not null default ''""'' comment('北京调用信息') VARCHAR(2000)"`
	Status       int       `xorm:"not null default 1 comment('处理状态: 1未处理;2已处理') INT(11)"`
	Lastdate     time.Time `xorm:"not null default 'current_timestamp()' comment('最后时间') TIMESTAMP"`
}

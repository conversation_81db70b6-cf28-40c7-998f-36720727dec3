package services

import (
	"context"
	"integral-center/proto/igc"
	"reflect"
	"testing"
)

func TestOrderService_ExpressImportTemplate(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *igc.OrderEmptyRequest
	}
	tests := []struct {
		name    string
		args    args
		wantOut *igc.ExpressImportTemplateResponse
		wantErr bool
	}{
		{},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{}
			gotOut, err := o.ExpressImportTemplate(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExpressImportTemplate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("ExpressImportTemplate() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestOrderService_ExpressImportList(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *igc.ExpressImportListRequest
	}
	tests := []struct {
		name    string
		args    args
		wantOut *igc.ExpressImportListResponse
		wantErr bool
	}{
		{
			args: args{in: &igc.ExpressImportListRequest{
				PageIndex: 1,
				PageSize:  10,
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{}
			gotOut, err := o.ExpressImportList(tt.args.ctx, tt.args.in)
			t.Log(gotOut)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExpressImportList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestOrderService_AWConfirmReceipt(t *testing.T) {
	type args struct {
		ctx     context.Context
		request *igc.AWOrderRequest
	}
	tests := []struct {
		name    string
		args    args
		wantOut *igc.OrderResponse
		wantErr bool
	}{
		{
			args: args{
				ctx: nil,
				request: &igc.AWOrderRequest{
					OrderSn: "11",
					ScrmId:  "1",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{}
			gotOut, err := o.AWConfirmReceipt(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("AWConfirmReceipt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && gotOut.Code != 200 {
				t.Errorf("AWConfirmReceipt() gotOut = %v, want %v", gotOut, tt.wantOut)
				return
			}
		})
	}
}

func TestOrderService_AWDeliveryDetail(t *testing.T) {
	type args struct {
		ctx     context.Context
		request *igc.AWOrderRequest
	}
	tests := []struct {
		name    string
		args    args
		wantOut *igc.AWDeliveryDetailResponse
		wantErr bool
	}{
		{
			args: args{
				ctx: nil,
				request: &igc.AWOrderRequest{
					OrderSn: "530702036356327009",
					ScrmId:  "5c1ad0f2e4e94f39a43c263c9b287d6d",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{}
			gotOut, err := o.AWDeliveryDetail(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("AWDeliveryDetail() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && gotOut.Code != 200 {
				t.Errorf("AWDeliveryDetail() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestOrderService_List(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *igc.OrderListRequest
	}
	tests := []struct {
		name    string
		args    args
		wantOut *igc.OrderListResponse
		wantErr bool
	}{
		{
			args: args{
				in: &igc.OrderListRequest{
					/*OrderSn:   "11",
					Mobile:    "1",*/
					StartDate: "2022-03-22",
					EndDate:   "2022-03-24",
					/*State:     "50",
					Type:      "1",
					//From: "",
					Channel:   "2",*/
					//GoodsName: "测试",
					PageSize:  0,
					PageIndex: 0,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{}
			gotOut, err := o.List(tt.args.ctx, tt.args.in)
			t.Log(gotOut)
			if (err != nil) != tt.wantErr {
				t.Errorf("List() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestOrderService_Detail(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *igc.OrderRequest
	}
	tests := []struct {
		name    string
		args    args
		wantOut *igc.OrderDetailResponse
		wantErr bool
	}{
		{
			args: args{
				in: &igc.OrderRequest{
					OrderSn: "490701891097795745",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{}
			gotOut, err := o.Detail(tt.args.ctx, tt.args.in)
			t.Log(gotOut, gotOut.Message)
			if (err != nil) != tt.wantErr {
				t.Errorf("Detail() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestOrderService_Export(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *igc.OrderListRequest
	}
	tests := []struct {
		name    string
		args    args
		wantOut *igc.OrderResponse
		wantErr bool
	}{
		{
			args: args{
				ctx: context.Background(),
				in:  &igc.OrderListRequest{
					/*OrderSn:   "11",
					Mobile:    "1",
					StartDate: "2022-03-22",
					EndDate:   "2022-03-24",
					State:     "50",
					Type:      "1",
					//From: "",
					Channel:   "2",
					GoodsName: "测试",
					PageSize:  0,
					PageIndex: 0,*/
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{}
			gotOut, err := o.Export(tt.args.ctx, tt.args.in)
			t.Log(gotOut)
			if (err != nil) != tt.wantErr {
				t.Errorf("Export() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestOrderService_ExportList(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *igc.OrderExportListRequest
	}
	tests := []struct {
		name    string
		args    args
		wantOut *igc.OrderExportListResponse
		wantErr bool
	}{
		{
			args: args{in: &igc.OrderExportListRequest{PageIndex: 2}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{}
			gotOut, err := o.ExportList(tt.args.ctx, tt.args.in)
			t.Log(gotOut)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExportList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestOrderService_ExpressCompanies(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *igc.OrderEmptyRequest
	}
	tests := []struct {
		name    string
		args    args
		wantOut *igc.ExpressCompaniesResponse
		wantErr bool
	}{
		{},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{}
			gotOut, err := o.ExpressCompanies(tt.args.ctx, tt.args.in)
			t.Log(gotOut)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExpressCompanies() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestOrderService_ExpressStore(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *igc.ExpressStoreRequest
	}
	tests := []struct {
		name    string
		args    args
		wantOut *igc.OrderResponse
		wantErr bool
	}{
		{
			args: args{in: &igc.ExpressStoreRequest{
				OrderSn:       "11",
				ShippingCode:  "物流单号",
				ShippingEcode: "SF",
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{}
			gotOut, err := o.ExpressStore(tt.args.ctx, tt.args.in)
			t.Log(gotOut)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExpressStore() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestOrderService_AWList(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *igc.AWOrderListRequest
	}
	tests := []struct {
		name    string
		args    args
		wantOut *igc.AWOrderListResponse
		wantErr bool
	}{
		{
			args: args{
				in: &igc.AWOrderListRequest{
					Type:      "2",
					ScrmId:    "02e2c06214aa4b7497199922af0e187c",
					PageSize:  0,
					PageIndex: 0,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{}
			gotOut, err := o.AWList(tt.args.ctx, tt.args.in)
			t.Log(gotOut)
			if (err != nil) != tt.wantErr {
				t.Errorf("AWList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestOrderService_AWDetail(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *igc.AWOrderRequest
	}
	tests := []struct {
		name    string
		args    args
		wantOut *igc.AWOrderDetailResponse
		wantErr bool
	}{
		{
			args: args{in: &igc.AWOrderRequest{
				OrderSn: "11",
				ScrmId:  "1",
			}},
		},
		{
			args: args{in: &igc.AWOrderRequest{
				OrderSn: "112",
				ScrmId:  "1",
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OrderService{}
			gotOut, err := o.AWDetail(tt.args.ctx, tt.args.in)
			t.Log(gotOut)
			if (err != nil) != tt.wantErr {
				t.Errorf("AWDetail() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestOrderService_AWStore(t *testing.T) {
	type args struct {
		ctx context.Context
		in  *igc.AWOrderStoreRequest
	}
	tests := []struct {
		name    string
		args    args
		wantOut *igc.OrderResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "11"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{}
			par := igc.AWOrderStoreRequest{
				OrgId:   1,
				GoodsId: 381,
				ScrmId:  "99fe9cbdc3a94e59a5649020da415ca9",
			}
			gotOut, err := o.AWStore(context.Background(), &par)
			if (err != nil) != tt.wantErr {
				t.Errorf("AWStore() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("AWStore() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

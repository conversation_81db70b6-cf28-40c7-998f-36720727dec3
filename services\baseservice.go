package services

import (
	"fmt"
	"github.com/go-redis/redis"
	_ "github.com/go-sql-driver/mysql"
	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"google.golang.org/grpc"
	"os"
	"runtime"
	"strconv"
	"sync"
	"time"
)

type BaseService struct {
}

var (
	Engine     *xorm.Engine
	once       sync.Once
	xormEngine *kit.DBEngine
)

func SetupDbConn() {
	xormEngine = kit.NewDBEngine(getDBDsn())
	//数据库定时探活
	go xormEngine.DBEngineCheck(xormEngine.NewXormEngineInterface, 3, 3)

}

// 关闭数据库连接
func CloseDbConn() {
	xormEngine.Engine.(*xorm.Engine).Close()
}

func getDBDsn(mySqlStr ...string) string {
	if len(mySqlStr) == 0 {
		mySqlStr = append(mySqlStr, config.GetString("mysql.datacenter")) //append(mySqlStr,"dbuser:ZQA!2sxQQ@(10.1.1.242:5532)/dc_order?charset=utf8mb4")
	}
	if len(mySqlStr[0]) == 0 {
		glog.Fatal("can't find mysql dsn")
		panic("can't find mysql url")
	}
	//glog.Info(mySqlStr[0])
	return mySqlStr[0]
}

// 建立数据库长连接
func GetDBConnLong(mySqlStr ...string) *xorm.Engine {
	if xormEngine == nil || xormEngine.Engine == nil {
		xormEngine = kit.NewDBEngine(getDBDsn(mySqlStr...))
	}

	engine := xormEngine.Engine.(*xorm.Engine)
	// 开发环境显示sql日志
	if os.Getenv("ASPNETCORE_ENVIRONMENT") == "" {
		engine.ShowSQL(true)
	}
	return engine
}

func init() {
	Engine = NewDbConn()
}

func NewDbConn() *xorm.Engine {
	once.Do(func() {
		Engine = newDbConn()
	})
	if Engine == nil {
		fmt.Println("engine is nil")
	}
	return Engine
}

func newDbConn() *xorm.Engine {
	mySqlStr := config.GetString("mysql.datacenter")
	//mySqlStr := "root:d&!89iCEGKOuVHkT@(39.107.46.194:13306)/datacenter?charset=utf8"

	// 本地批量处理错误mq
	//mySqlStr = "wangy:adfar8THJGAV@(113.96.44.27:5532)/datacenter?charset=utf8mb4"
	if len(mySqlStr) == 0 {
		glog.Fatal("can't find mysql.datacenter url")
		panic("can't find mysql.datacenter url")
	}

	engine, err := xorm.NewEngine("mysql", mySqlStr)

	if err != nil {
		glog.Fatal("mysql.datacenter connect fail ", err, mySqlStr)
		panic(err)
	}

	//engine.ShowSQL()

	location, err := time.LoadLocation("Asia/Shanghai")
	engine.SetTZLocation(location)
	return engine
}

//获取base服务客户端
func (b *BaseService) GetBaseClient() *grpc.ClientConn {
	address := config.GetString("grpc.base")
	if address == "" || runtime.GOOS == "windows" {
		address = "localhost:8888"
	}
	conn, err := grpc.Dial(address, grpc.WithInsecure())
	if err != nil {
		glog.Errorf("did not connect: %v", err)
	}
	return conn
}

//redis連接
func GetRedisConn() *redis.Client {
	DB, _ := strconv.Atoi(config.GetString("redis.DB"))
	client := redis.NewClient(&redis.Options{
		Addr:     config.GetString("redis.Addr"),
		Password: config.GetString("redis.Password"),
		DB:       DB,
	})
	return client
}

var (
	redisHandle *redis.Client
)

//redis长连接
func GetRedisConnLong() *redis.Client {
	if redisHandle != nil {
		_, err := redisHandle.Ping().Result()
		if err == nil {
			return redisHandle
		}
	}

	var db = cast.ToInt(config.GetString("redis.DB"))
	var addr = config.GetString("redis.Addr")
	var pwd = config.GetString("redis.Password")

	//addr = "*************:6379"
	//pwd = "MkdGH*3ldf"

	redisHandle = redis.NewClient(&redis.Options{
		Addr:         addr,
		Password:     pwd,
		DB:           db,
		MinIdleConns: 10,
		IdleTimeout:  60,
	})
	_, err := redisHandle.Ping().Result()
	if err != nil {
		panic(err)
	}

	return redisHandle
}

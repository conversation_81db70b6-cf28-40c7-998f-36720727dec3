package models

import (
	"time"
)

type RefundOrder struct {
	Id              string    `xorm:"not null pk default ''uuid()'' comment('退款Id') VARCHAR(36)"`
	Refundsn        string    `xorm:"not null comment('退款单号') VARCHAR(50)"`
	Ordersn         string    `xorm:"not null comment('原始销售单号') VARCHAR(50)"`
	Createtime      time.Time `xorm:"not null default 'current_timestamp()' comment('创建时间') TIMESTAMP"`
	Status          string    `xorm:"default 'NULL' comment('管家婆审核状态') VARCHAR(50)"`
	Refundtypesn    string    `xorm:"default 'NULL' comment('售后单类型 JustRefund=仅退款 RefundAndGoods=退款退货') VARCHAR(50)"`
	Reasoncode      string    `xorm:"default ''01'' comment('前段没有判定，管易:007，全渠道:01') VARCHAR(50)"`
	Refundremark    string    `xorm:"default 'NULL' comment('售后单备注') VARCHAR(200)"`
	Refundtype      int       `xorm:"not null default 1 comment('申请类型:1为退款,2为退货，默认为1') INT(11)"`
	Discountamount  string    `xorm:"not null default ''''0'''' comment('优惠金额') VARCHAR(20)"`
	Postfee         string    `xorm:"not null default ''''0'''' comment('运费') VARCHAR(20)"`
	Refundreason    string    `xorm:"not null comment('退款原因') VARCHAR(500)"`
	Warehouseincode string    `xorm:"default 'NULL' comment('对应仓库id') VARCHAR(50)"`
	Expressname     string    `xorm:"default 'NULL' comment('退货快递名称') VARCHAR(100)"`
	Expressnum      string    `xorm:"default 'NULL' comment('退货快递单号') VARCHAR(50)"`
	Refundamount    string    `xorm:"default ''''0'''' comment('退款金额') VARCHAR(20)"`
	Ordersource     int       `xorm:"not null default 1 comment('仓库所属1:(a8 or 全渠道)  2:管易  3:门店') INT(11)"`
	Tradecode       string    `xorm:"default 'NULL' comment('管易销售订单单据编号') VARCHAR(50)"`
	ExpressInfo     string    `xorm:"default 'NULL' comment('快递信息，JSON格式') VARCHAR(500)"`
	OrderFrom       int       `xorm:"default NULL comment('渠道id(1-阿闻，2-美团，3-饿了么)') TINYINT(4)"`
	ApplyOpUserType string    `xorm:"default 'NULL' comment('发起退款角色还是商家；仅适用于支持退货退款的商家。') VARCHAR(100)"`
	Fullrefund      int       `xorm:"default 1 comment('部分退还是整单退，1整单 2部分') INT(11)"`
	RefundState     int       `xorm:"default NULL comment('订单退款状态 0:未退款 1:退款完成 2:退款中') INT(11)"`
}

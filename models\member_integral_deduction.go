package models

import (
	"time"
)

type MemberIntegralDeduction struct {
	Id                  int       `xorm:"not null pk autoincr INT(11)"`
	Memberid            string    `xorm:"default '''' comment('用户id') CHAR(16)"`
	Deductintegralid    string    `xorm:"default '''' comment('抵扣记录id(type为2)') VARCHAR(50)"`
	Integralid          string    `xorm:"default '''' comment('被抵扣积分记录id') VARCHAR(50)"`
	Deductintegralcount int       `xorm:"default NULL comment('抵扣积分数') INT(11)"`
	Createdate          time.Time `xorm:"default 'NULL' comment('抵扣时间') DATETIME"`
}

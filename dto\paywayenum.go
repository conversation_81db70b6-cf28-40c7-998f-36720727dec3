package dto

//支付方式枚举
type PayWayEnum int32

const (
	Cash          PayWayEnum = 1  //现金
	WeChat        PayWayEnum = 10 //微信
	Alipay        PayWayEnum = 11 //支付宝
	Points        PayWayEnum = 2  //积分
	Coupon        PayWayEnum = 3  //优惠券
	OfflineCoupon PayWayEnum = 4  //线下优惠券
	RedPack       PayWayEnum = 5  //红包
)

func (integraltype PayWayEnum) String() string {
	switch integraltype {
	case Cash:
		return "现金"
	case WeChat:
		return "微信"
	case Alipay:
		return "支付宝"
	case Points:
		return "积分"
	case Coupon:
		return "优惠券"
	case OfflineCoupon:
		return "线下优惠券"
	case RedPack:
		return "红包"
	default:
		return "UNKNOWN"
	}
}

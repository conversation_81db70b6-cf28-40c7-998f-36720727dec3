package utils

import (
	"bytes"
	"crypto/md5"
	"crypto/rc4"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"regexp"
	"sort"
	"strconv"
	"time"

	"github.com/limitedlee/microservice/common/config"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

var securityKey string

type DecryptRequest struct {
	CiphertextsArray []string `json:"array"`
	AppId            int      `json:"app-id"`
	UserId           string   `json:"user-id"`
	Timestamp        int      `json:"timestamp"`
	Sign             string   `json:"sign"`
}

func sign(secretCode string, biz map[string]string) string {
	if biz == nil {
		biz = make(map[string]string)
	}
	//取出map所有key进行正序排序
	keys := make([]string, 0, len(biz))
	for k := range biz {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	// 按顺序进行拼接字符串
	builder := new(bytes.Buffer)
	for _, k := range keys {
		builder.WriteString(k)
		builder.WriteString(biz[k])
	}

	// 最后拼接secretCode
	builder.WriteString(secretCode)
	// 生成md5
	h := md5.New()
	h.Write(builder.Bytes())
	signData := hex.EncodeToString(h.Sum(nil))
	return signData
}

func init() {
	url := config.GetString("awen_url") + "/data-security/get-key"
	req := DecryptRequest{
		AppId:     10,
		UserId:    "10",
		Timestamp: cast.ToInt(time.Now().Unix()),
	}

	params := make(map[string]string)
	params["app-id"] = "10"
	params["user-id"] = "10"
	params["timestamp"] = strconv.FormatInt(time.Now().Unix(), 10)
	req.Sign = sign(config.GetString("security_code"), params)

	var reqBody bytes.Buffer
	reqBody.Write(kit.JsonEncodeByte(req))
	resp, err := http.Post(url, "application/json", &reqBody)
	if err != nil {
		glog.Error("请求data-security获取key失败,error:", err.Error())
		return
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		glog.Error("请求data-security获取key解析失败,error:", err.Error())
		return
	}
	type Resp struct {
		Key string `json:"key"`
	}
	var res Resp
	if err = json.Unmarshal(body, &res); err != nil {
		glog.Error("请求data-security获取key反序列失败,error:", err.Error())
		return
	}
	if res.Key == "" {
		panic("请求data-security获取key不能为空:" + string(body))
	}
	securityKey = res.Key
}

// MobileEncrypt 手机号加密
func MobileEncrypt(mobile string) string {
	if len(mobile) == 0 {
		return ""
	}
	c, _ := rc4.NewCipher([]byte(securityKey))
	src := []byte(mobile)
	dst := make([]byte, len(src))
	c.XORKeyStream(dst, src)
	return base64.StdEncoding.EncodeToString(dst)
}

// MobileDecrypt 手机号rc4解密
func MobileDecrypt(ciphertext string) string {
	if len(ciphertext) == 0 {
		return ""
	}
	// 是明文手机号不处理
	if ok, _ := regexp.MatchString(`^\d+$`, ciphertext); ok {
		return ciphertext
	}
	c, _ := rc4.NewCipher([]byte(securityKey))
	src, _ := base64.StdEncoding.DecodeString(ciphertext)
	dst := make([]byte, len(src))
	c.XORKeyStream(dst, src)

	return string(dst)
}

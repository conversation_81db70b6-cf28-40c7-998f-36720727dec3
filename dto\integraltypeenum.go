package dto

//积分类型枚举
type IntegralTypeEnum int32

const (
	ConsultingOrderPay       IntegralTypeEnum = 11 //咨询服务
	ConsultingOrderRefund    IntegralTypeEnum = 12 //取消咨询
	PhysicalStoreOrderPay    IntegralTypeEnum = 21 //门店消费
	PhysicalStoreOrderRefund IntegralTypeEnum = 22 //门店退货
	BookingOrderPay          IntegralTypeEnum = 31 //预约服务
	BookingOrderRefund       IntegralTypeEnum = 32 //取消预约
	ECommerceOrderPay        IntegralTypeEnum = 41 //商城消费
	ECommerceOrderRefund     IntegralTypeEnum = 42 //商城退货
	IntegralOrderPay         IntegralTypeEnum = 52 //积分兑换
)

func (integraltype IntegralTypeEnum) String() string {
	switch integraltype {
	case ConsultingOrderPay:
		return "咨询服务"
	case ConsultingOrderRefund:
		return "取消咨询"
	case PhysicalStoreOrderPay:
		return "门店消费"
	case PhysicalStoreOrderRefund:
		return "门店退货"
	case BookingOrderPay:
		return "预约服务"
	case BookingOrderRefund:
		return "取消预约"
	case ECommerceOrderPay:
		return "商城消费"
	case ECommerceOrderRefund:
		return "商城退货"
	case IntegralOrderPay:
		return "积分兑换"
	default:
		return "UNKNOWN"
	}
}

package order

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"html"
	"integral-center/helpers"
	"integral-center/models"
	"integral-center/pkg/http/zilong"
	"integral-center/proto/ac"
	"integral-center/proto/cc"
	"integral-center/proto/igc"
	"integral-center/proto/oc"
	"integral-center/proto/sh"
	"integral-center/utils"
	"math/rand"
	"regexp"
	"strings"
	"time"

	"github.com/go-xorm/xorm"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"google.golang.org/grpc/metadata"
)

// 兑换处理器
type storeHandler struct {
	Product   *models.IntegralProduct // 兑换产品
	UpetGoods *models.UpetGoods       // 虚拟商品对应商城商品信息
	Address   *models.UpetAddress     // 用户地址信息
	Member    *models.UpetMember      // 会员信息

	IntegralExchange        *models.IntegralExchange // 兑换订单信息
	IntegralExchangeGoods   *models.IntegralExchangeGoods
	IntegralExchangeAddress *models.IntegralExchangeAddress
	Db                      *xorm.Engine // 中台数据库
	UpetDb                  *xorm.Engine // 商城数据库
	OrgId                   int32        //机构ID
}

// Store 订单提交（立即兑换）
func Store(in *igc.AWOrderStoreRequest) (err error) {
	r := utils.ConnectRedis()
	defer r.Close()

	lockKey := "integral-center:order:store:" + in.ScrmId
	if lock, err := r.SetNX(lockKey, 1, time.Second*60).Result(); err != nil {
		return errors.New("获取锁失败 " + err.Error())
	} else if !lock {
		return errors.New("兑换处理中，请不要重复提交")
	}
	defer r.Del(lockKey)

	// 第一步初始化
	h, err := initHandler(in)
	if err != nil {
		return
	}

	if err = h.checkBlackList(); err != nil { // 检查白名单
		return
	} else if err = h.validate(); err != nil { // 第二步验证数据
		return
	} else if err = h.setExchange(); err != nil { // 第三步设置积分订单数据
		return
	} else if err = h.pushToDatacenter(); err != nil { // 第四步同步中台订单
		return
	} else if err = h.store(); err != nil { // 第五步落地订单
		h.rollback()
		return
	} else if err = h.payNotify(); err != nil { // 第六步通知支付状态
		h.rollback()
		return
	}

	//积分兑换爱心币
	if h.IntegralExchange.Type == models.IETypeVr && h.Product.GoodType == models.IEGoodsCoin {
		//第一扣积分
		IntegralChangeMqData := new(oc.IntegralNotify)
		IntegralChangeMqData.Oldordersn = h.IntegralExchange.OrderSn
		IntegralChangeMqData.Integraltype = models.IntegralTypeCoin
		IntegralChangeMqData.Notifytime = time.Now().Format(kit.DATETIME_LAYOUT)
		IntegralChangeMqData.MemberId = in.ScrmId
		IntegralChangeMqData.Mobile = h.Member.MemberMobile
		IntegralChangeMqData.Integral = h.Product.Points
		pubRes := PublishIntegralChange(IntegralChangeMqData)

		if !pubRes {
			h.rollback()
			return errors.New("积分兑换爱心币减扣积分失败")
		}

		//增加爱心币
		req := new(ac.GiveCoinRequest)
		req.ScrmId = in.ScrmId
		req.Mobile = h.Member.MemberMobile
		req.Num = h.Product.CoinNum
		req.Remark = "积分兑换爱心币"

		centerClient := ac.GetActivityCenterClient()

		resp, err := centerClient.UC.GiveCoin(centerClient.Ctx, req)
		if err != nil {
			glog.Error("积分兑换爱心币增加爱心币出错  请求参数：", req, "响应结果：", resp, "错误信息：", err)
			return errors.New("积分兑换爱心币增加爱心币出错")
		}

	}

	// 是优惠券的调优惠券发送接口
	if h.IntegralExchange.Type == models.IETypeCoupon {
		if h.Product.GoodType == models.IEGoodsShop {
			typeCouponRetrySend(h.IntegralExchange, cast.ToInt32(h.Product.ScrmInfo))
		} else if h.Product.GoodType == models.IEGoodsMall { //商城券
			client := sh.GetUpetCenterClient()
			defer client.Conn.Close()
			req := &sh.IssueAwardByMallCouponRequest{
				CouponId: h.Product.ScrmInfo,
				UserId:   in.ScrmId,
			}
			resp, err := client.VS.IssueAwardByMallCoupon(client.Ctx, req)
			if err != nil {
				glog.Error("积分兑换商城券发放商城券出错  请求参数：", req, "响应结果：", resp, "错误信息：", err)
			} else if resp.Code != 200 {
				glog.Error("积分兑换商城券发放商城券失败  请求参数：", req, "响应结果：", resp)
			}
			//更新券码
			if len(resp.Data) > 0 {
				_, err = models.GetDBConn().Table("dc_order.integral_exchange").Where("id = ?", h.IntegralExchange.Id).Update(&models.IntegralExchange{
					CouponCode:    resp.Data[0].CouponCode,
					CouponEndTime: int32(resp.Data[0].CouponEndTime),
					FinnshedTime:  int32(time.Now().Unix()),
				})
				if err != nil {
					glog.Info("更新商城券积分订单券码失败:", h.IntegralExchange.Id, "-", err)
				}
			}
		}
	}

	return nil
}

// 第一步 初始化处理器
func initHandler(in *igc.AWOrderStoreRequest) (h *storeHandler, err error) {
	h = &storeHandler{
		Product:   new(models.IntegralProduct),
		UpetGoods: new(models.UpetGoods),
		Address:   new(models.UpetAddress),
		Member:    new(models.UpetMember),
		Db:        models.GetDBConn(),
		UpetDb:    models.GetUpetDBConn(),
		OrgId:     in.OrgId,
	}

	if has, err := h.Db.Table("dc_product.integral_product").Where("id = ? and store_id=?", in.GoodsId, in.OrgId).Get(h.Product); err != nil {
		return nil, errors.New("查询商品出错 " + err.Error())
	} else if !has {
		return nil, errors.New("商品不存在")
	}
	if h.Product.Show < 1 {
		return nil, errors.New("商品已下架")
	}
	if h.Product.Storage < 1 {
		return nil, errors.New("该礼品已兑换完")
	}

	sql := "user_level_id,member_id,scrm_user_id,member_name,member_mobile,weixin_mini_openid"
	if in.OrgId == 2 {
		sql = "user_level_id,member_id,scrm_user_id,member_name,member_mobile,weixin_mini_openid2 as weixin_mini_openid"
	}

	if has, err := h.UpetDb.Table("upet_member").Select(sql).
		Where("scrm_user_id = ?", in.ScrmId).Get(h.Member); err != nil {
		return nil, errors.New("查询会员出错 " + err.Error())
	} else if !has {
		return nil, errors.New("会员不存在")
	}

	// 实物订单，查询地址
	if h.Product.Type == models.IETypeGoods {
		if has, err := h.UpetDb.Table("upet_address").Where("member_id = ? and address_id = ?", h.Member.MemberId, in.AddressId).
			Get(h.Address); err != nil {
			return nil, errors.New("查询地址出错 " + err.Error())
		} else if !has {
			return nil, errors.New("地址不存在")
		}
	}

	// 虚拟订单查电商商品信息
	if h.Product.Type == models.IETypeVr {
		if len(h.Product.ScrmInfo) > 0 {
			h.UpetGoods, err = models.GetValidVirtualGoods(cast.ToInt32(h.Product.ScrmInfo), in.OrgId)
			if err != nil {
				return nil, errors.New("兑换" + err.Error())
			}
		}

		// 相对有效时间，当天最后一秒
		if h.UpetGoods.ValidityType == 2 {
			y, m, d := time.Now().AddDate(0, 0, int(h.UpetGoods.VirtualIndate)).Date()
			h.UpetGoods.VirtualIndate = int32(time.Date(y, m, d, 0, 0, -1, 0, time.Local).Unix())
		}
	}

	return h, nil
}

// 提交订单检查黑名单
func (h *storeHandler) checkBlackList() (err error) {
	ccClient := cc.GetCustomerCenterLongClient()
	checkReq := &cc.CheckBlackListReq{Mobile: h.Member.MemberMobile}
	if h.Product.Type == models.IETypeGoods {
		checkReq.ReceiverMobile = h.Address.MobPhone
	}
	if out, err := ccClient.User.CheckBlackList(context.Background(), checkReq); err != nil {
		return err
	} else if out.Code != 200 {
		return errors.New(out.Message)
	}

	return
}

// 第二步 验证是否可兑换
func (h *storeHandler) validate() error {
	// 限制时间
	if h.Product.IsLimitTime > 0 {
		// 非0值也非时间戳0值，活动时间大于当前时间
		if h.Product.StartTime.Unix() > 0 && h.Product.StartTime.After(time.Now()) {
			return errors.New("兑换活动还未开始")
		}
		// 非0值也非时间戳0值，结束时间小于当前时间
		if h.Product.EndTime.Unix() > 0 && h.Product.EndTime.Before(time.Now()) {
			return errors.New("兑换活动已经结束")
		}
	}

	// 限制购买数量
	if h.Product.IsLimit > 0 && h.Product.LimitNum > 0 {
		var count int64
		if _, err := h.Db.Table("dc_order.integral_exchange_goods").Alias("eg").Join("inner", "dc_order.integral_exchange e", "e.id = eg.exchange_id").
			Select("sum(eg.qty)").Where("e.delete_time = 0").
			Where("eg.product_id =? and e.scrm_id = ?", h.Product.Id, h.Member.ScrmUserId).
			Where("e.state != ?", models.IEStateCanceled).Get(&count); err != nil {
			return errors.New("查询购买数量出错 " + err.Error())
		} else if count >= int64(h.Product.LimitNum) {
			return errors.New("您已达到该礼品的最大兑换数，不能再兑换")
		}
	}

	// 限制会员，直接查会员卡
	if h.Product.LimitMgrade > 0 {
		//限制的等级没有数据 说明是老版本 老版本仅限制是不是会员
		//新版本是按照具体的等级去限制的
		if h.Product.UserLevelIds == "" {
			if has, err := h.Db.Table("datacenter.member_card_relation").Where("cardtype = 2 and vipstatus = 1").
				Where("userid = ?", h.Member.ScrmUserId).Exist(); err != nil {
				return errors.New("查询会员卡状态出错 " + err.Error())
			} else if !has {
				return errors.New("仅会员卡用户可兑换")
			}
		} else {
			limitLevel := strings.Split(h.Product.UserLevelIds, ",")
			strUserLevel := cast.ToString(h.Member.UserLevelId)
			allow := false
			for _, level := range limitLevel {
				if level == strUserLevel {
					allow = true
					break
				}
			}
			if !allow {
				return errors.New("会员等级不符合兑换要求")
			}
		}
	}

	// 实时查询积分
	h.Member.MemberPoints = 0
	if _, err := h.Db.Table("datacenter.member_integral_info").
		Where("memberid = ?", h.Member.ScrmUserId).Select("integral").Get(&h.Member.MemberPoints); err != nil {
		return errors.New("查询积分出错 " + err.Error())
	} else if h.Member.MemberPoints < h.Product.Points {
		return errors.New("积分不足")
	}

	// 验证门店券有效性
	if h.Product.Type == models.IETypeCoupon {
		if h.Product.GoodType == models.IEGoodsShop {
			if _, template, err := zilong.CouponTemplateDetail(cast.ToInt32(h.Product.ScrmInfo)); err != nil {
				return err
			} else if template.StatusValue > 40 || template.StatusValue < 30 {
				return errors.New("门店券状态 " + template.Status + " 不允许兑换")
			} else if template.Inventory < 1 {
				return errors.New("门店券库存不足")
			}
		} else if h.Product.GoodType == models.IEGoodsMall {
			err := h.validateMallCoupon()
			if err != nil {
				return err
			}
		}
	}
	return nil
}

// 第三步 设置积分订单数据
func (h *storeHandler) setExchange() error {
	var state int32
	switch h.Product.Type {
	case models.IETypeVr:
		//爱心币订单状态直接为完成
		if h.Product.GoodType == models.IEGoodsCoin {
			state = models.IEStateFinished
		} else {
			state = models.IEStateUse
		}
	case models.IETypeCoupon:
		state = models.IEStateFinished
	default:
		state = models.IEStateDelivery
	}

	h.IntegralExchange = &models.IntegralExchange{
		Type:          h.Product.Type,
		OrderSn:       makeOrderSn(h.Member.MemberId),
		UserId:        h.Member.MemberId,
		ScrmId:        h.Member.ScrmUserId,
		UserName:      h.Member.MemberName,
		UserMobile:    h.Member.MemberMobile[0:3] + "****" + h.Member.MemberMobile[7:],
		EncryptMobile: utils.MobileEncrypt(h.Member.MemberMobile),
		IntegralTotal: h.Product.Points,
		// Remark:        "",
		State:   state,
		From:    models.IEFromMini,
		Gtype:   h.Product.GoodType,
		AddTime: int32(time.Now().Unix()),
	}

	h.IntegralExchangeGoods = &models.IntegralExchangeGoods{
		ProductId:   h.Product.Id,
		GoodsId:     h.Product.ScrmInfo,
		GoodsName:   h.Product.Name,
		GoodsPrice:  cast.ToFloat32(h.Product.Price),
		GoodsSerial: h.Product.Serial,
		Integral:    h.Product.Points,
		Qty:         1,
		GoodsImage:  h.Product.Image,
	}

	if h.Product.Type == models.IETypeGoods {
		h.IntegralExchangeAddress = &models.IntegralExchangeAddress{
			Name:          h.Address.TrueName,
			AreaId:        h.Address.AreaId,
			AreaInfo:      h.Address.AreaInfo,
			Address:       h.Address.Address,
			Mobile:        h.Address.MobPhone[0:3] + "****" + h.Address.MobPhone[7:],
			EncryptMobile: utils.MobileEncrypt(h.Address.MobPhone),
		}
	} else if h.Product.Type == models.IETypeVr {
		// 虚拟商品也标记过期时间，便于定时任务标记过期状态
		h.IntegralExchange.CouponEndTime = h.UpetGoods.VirtualIndate
	}

	return nil
}

// 第四步 推送订单到数据中心
func (h *storeHandler) pushToDatacenter() error {
	req, err := h.toMtAddOrderRequest()
	if err != nil {
		return err
	}

	client := oc.GetOrderServiceClient()
	defer client.Close()
	ctx := metadata.AppendToOutgoingContext(client.Ctx, "grpc_context", kit.JsonEncode(map[string]interface{}{
		"Channel": map[string]interface{}{
			"ChannelId": 5,
			"UserAgent": 3, // 渠道来源,1-Android,2-iOS,3-小程序
		},
	}))
	req.OrgId = h.OrgId
	if out, err := client.Cart.MtSubmitOrder(ctx, req); err != nil {
		return err
	} else if out.Code != 200 {
		return errors.New(out.Message)
	}

	h.IntegralExchange.ErpStatus = 1
	h.IntegralExchange.ErpMobile = h.Member.MemberMobile
	h.IntegralExchange.ErpTime = int32(time.Now().Unix())

	return nil
}

// 第五步 兑换订单落地
func (h *storeHandler) store() (err error) {
	// 先插入待支付的商城虚拟订单，便于设置exchange表vr_orderid
	if h.Product.Type == models.IETypeVr {
		if id, err := h.typeVrAddMallOrder(); err != nil {
			return err
		} else {
			h.IntegralExchange.VrOrderId = id
		}
	}

	session := h.Db.NewSession()
	defer session.Close()
	_ = session.Begin()

	if _, err := session.Table("dc_order.integral_exchange").Insert(h.IntegralExchange); err != nil {
		return errors.New("插入兑换订单出错 " + err.Error())
	}

	h.IntegralExchangeGoods.ExchangeId = h.IntegralExchange.Id
	if _, err := session.Table("dc_order.integral_exchange_goods").Insert(h.IntegralExchangeGoods); err != nil {
		return errors.New("插入兑换订单商品出错 " + err.Error())
	}

	if h.Product.Type == models.IETypeGoods {
		h.IntegralExchangeAddress.ExchangeId = h.IntegralExchange.Id
		if _, err := session.Table("dc_order.integral_exchange_address").Insert(h.IntegralExchangeAddress); err != nil {
			return errors.New("插入兑换订单地址出错 " + err.Error())
		}
	}

	// 更新库存数据
	if _, err := session.Exec("update dc_product.integral_product set storage = storage -1,sale_num = sale_num + 1 "+
		"where id = ?", h.Product.Id); err != nil {
		return errors.New("更新库存销量数据出错 " + err.Error())
	}

	return session.Commit()
}

// 第六步，通知支付状态
func (h *storeHandler) payNotify() (err error) {
	client := oc.GetOrderServiceClient()
	defer client.Close()

	req := &oc.OrderPayNotifyRequest{
		OrderSn:   h.IntegralExchange.OrderSn,
		PaySn:     fmt.Sprintf("%s%d", h.IntegralExchange.OrderSn, h.IntegralExchange.Id),
		PayTime:   time.Now().Format(kit.DATETIME_LAYOUT),
		PayMode:   2,
		PayAmount: h.IntegralExchange.IntegralTotal,
	}

	if out, err := client.RPC.OrderPayNotify(client.Ctx, req); err != nil {
		return errors.New("通知支付出错 " + err.Error())
	} else if out.Code != 200 {
		return errors.New(out.Message)
	}

	return nil
}

// 验证商城券是否可兑换 供外部使用
func ValidateMallCoupon(db *xorm.Engine, ScrmInfo string, storage int32) error {
	h := new(storeHandler)
	if db == nil {
		h.UpetDb = models.GetUpetDBConn()
	} else {
		h.UpetDb = db
	}
	h.Product = &models.IntegralProduct{ScrmInfo: ScrmInfo, Storage: storage}
	return h.validateMallCoupon()
}

// 验证商城券是否可兑换
func (h *storeHandler) validateMallCoupon() error {
	voucher := new(models.UpetVoucherTemplate)
	has, err := h.UpetDb.Table("upet_voucher_template").
		Select("voucher_t_start_date,voucher_t_end_date,voucher_t_state,voucher_t_total,voucher_t_giveout").
		Where("voucher_t_id=? and voucher_t_store_id = 1", h.Product.ScrmInfo).Get(voucher)
	if err != nil {
		return err
	}
	if !has {
		return errors.New("该优惠券id不存在")
	}
	if voucher.VoucherTState == 2 {
		return errors.New("该优惠券已失效")
	}
	//再判断一下开始时间与结束时间
	now := time.Now().Unix()
	if voucher.VoucherTEndDate > 0 {
		intEndDate := int64(voucher.VoucherTEndDate)
		if now > intEndDate {
			return errors.New("该优惠券已过有效期")
		}
	}
	if h.Product.Storage > voucher.VoucherTTotal-voucher.VoucherTGiveout {
		if voucher.VoucherTTotal <= voucher.VoucherTGiveout {
			return errors.New("该优惠券已无库存")
		} else {
			return errors.New("兑换库存不能大于券的剩余库存")
		}
	}
	return nil
}

// 订单关联数据回滚
// 包括两个部分，回滚商城虚拟订单，软删除积分订单
func (h *storeHandler) rollback() {
	// 虚拟订单标记为未支付
	if h.IntegralExchange.VrOrderId > 0 {
		if _, err := h.UpetDb.Table("upet_vr_order").Where("order_id = ?", h.IntegralExchange.VrOrderId).Update(&models.UpetVrOrder{
			PaymentCode: "",
			PaymentTime: 0,
			TradeNo:     "",
		}); err != nil {
			glog.Info("积分商城 OrderAWStore 订单号：", h.IntegralExchange.OrderSn, "，回撤商城支付状态时出错：", err.Error())
		}
	}
	// 积分订单标记为删除
	if h.IntegralExchange.Id > 0 {
		if _, err := h.Db.Table("dc_order.integral_exchange").Where("id = ?", h.IntegralExchange.Id).Update(map[string]interface{}{
			"delete_time": int32(time.Now().Unix()),
			"state":       models.IEStatePay,
		}); err != nil {
			glog.Info("积分商城 OrderAWStore 订单号：", h.IntegralExchange.OrderSn, "，回撤积分订单时出错：", err.Error())
		}
	}
}

// 生成订单号
// 模拟 php point_snOrder 生成算法
// 生成积分兑换订单编号(两位随机 + 从2000-01-01 00:00:00 到现在的秒数+微秒+会员ID%1000)，该值会传给第三方支付接口
// 长度 =2位 + 10位 + 3位 + 3位  = 18位
// return mt_rand(10, 99)
// . sprintf('%010d', time() - 946656000)
// . sprintf('%03d', (float)microtime() * 1000)
// . sprintf('%03d', (int)$member_id % 1000);
func makeOrderSn(memberId int32) string {
	now := time.Now()
	rand.Seed(now.UnixNano())

	id := cast.ToString(memberId)
	if len(id) > 3 {
		id = id[len(id)-3:]
	}
	return fmt.Sprintf("%d%010d%03d%03s",
		rand.Intn(89)+10,                   // 生成10-99的概率数据
		now.Unix()-946656000,               // 从2000-01-01 00:00:00 到现在的秒数
		now.UnixNano()/1e6-now.Unix()*1000, // 微秒最后3位
		id,                                 // 会员id后3位
	)
}

// 转换到中台提交订单请求数据
func (h *storeHandler) toMtAddOrderRequest() (*oc.MtAddOrderRequest, error) {
	req := &oc.MtAddOrderRequest{
		OrderStatus:      10,
		OrderStatusChild: 20101,
		LogisticsCode:    "0000",
		BuyerMemo:        "积分兑换",
		DeliveryType:     8, // 订单类型 8积分订单
		Freight:          0,
		Total:            h.IntegralExchange.IntegralTotal, // 订单总金额
		GoodsTotal:       h.IntegralExchange.IntegralTotal, // 商品总金额
		OrderType:        8,                                // 订单类型 8积分订单,
		OrderSn:          h.IntegralExchange.OrderSn,
		OpenId:           h.Member.WeixinMiniOpenid,
		MemberId:         h.Member.ScrmUserId,
		MemberTel:        h.Member.MemberMobile,
		MemberName:       h.Member.MemberName,

		OrderProductModel: []*oc.OrderProductModel{
			{
				Image:        helpers.GetIntegralProductUrl(h.Product.Image, true),
				Number:       1,
				DeliverNum:   1,
				Price:        h.IntegralExchangeGoods.Integral,
				PayPrice:     h.IntegralExchangeGoods.Integral,
				MarkingPrice: h.IntegralExchangeGoods.Integral,
				SkuPayTotal:  h.IntegralExchangeGoods.Integral,
				PaymentTotal: h.IntegralExchangeGoods.Integral,
				ProductName:  h.Product.Name,
				Sku:          h.Product.ScrmInfo,
				ProductId:    h.Product.ScrmInfo,
			},
		},
	}

	// 实物订单
	if h.Product.Type == models.IETypeGoods {
		// 区域切片
		areaSlice := strings.Split(h.Address.AreaInfo, " ")
		if len(areaSlice) < 3 {
			return nil, errors.New("地址区域信息错误")
		}
		req.IsVirtual = 0
		req.ReceiverAddress = h.Address.AreaInfo + h.Address.Address // 收件地址
		req.ReceiverState = areaSlice[0]                             // 省份
		req.ReceiverCity = areaSlice[1]                              // 城市
		req.ReceiverDistrict = areaSlice[2]                          // 区域
		req.ReceiverName = h.Address.TrueName                        // 收件人
		req.ReceiverPhone = h.Address.MobPhone                       // 收件电话
		req.OrderPayType = "03"                                      // 支付类型 03：非分销订单
		req.OrderProductModel[0].ProductType = 1                     // 商品类型1-实物商品，2-虚拟商品，3-组合商品

		if h.Product.GoodType == 1 {
			req.OrderProductModel[0].IsThirdProduct = 1 // 是否是第三方商品
		}
	} else { // 虚拟订单
		req.IsVirtual = 1
		req.ReceiverPhone = h.Member.MemberMobile
		req.OrderPayType = "05"                  // 05：团单订单
		req.OrderProductModel[0].ProductType = 2 // 商品类型1-实物商品，2-虚拟商品，3-组合商品

		// 虚拟商品填充有效期信息、取商品价格
		if h.Product.Type == models.IETypeVr {
			req.OrderProductModel[0].TermType = 1 // 1固定时间
			req.OrderProductModel[0].TermValue = h.UpetGoods.VirtualIndate
			req.OrderProductModel[0].VirtualInvalidRefund = h.UpetGoods.VirtualInvalidRefund

			fen := int32(kit.YuanToFen(h.UpetGoods.GoodsPrice))
			req.OrderProductModel[0].Price = fen
			req.OrderProductModel[0].PayPrice = fen
			req.OrderProductModel[0].MarkingPrice = fen
			req.OrderProductModel[0].SkuPayTotal = fen
			req.OrderProductModel[0].PaymentTotal = fen
		}
	}

	return req, nil
}

// 针对虚拟商品，添加商城虚拟订单
func (h *storeHandler) typeVrAddMallOrder() (int32, error) {
	// 提取php序列化数组规格值
	var specs []string
	reg, _ := regexp.Compile(`:\"(.*?)\";`)
	specNames := reg.FindAllStringSubmatch(h.UpetGoods.SpecName, -1)
	specValues := reg.FindAllStringSubmatch(h.UpetGoods.GoodsSpec, -1)
	for i, sn := range specNames {
		if len(specValues) > i {
			specs = append(specs, sn[1]+"："+html.UnescapeString(specValues[i][1]))
		}
	}

	vrOrder := &models.UpetVrOrder{
		OrderSn:       cast.ToInt64(h.IntegralExchange.OrderSn),
		StoreId:       1,
		StoreName:     "瑞海联官方自营",
		BuyerId:       h.Member.MemberId,
		BuyerName:     h.Member.MemberName,
		BuyerPhone:    h.Member.MemberMobile[0:3] + "****" + h.Member.MemberMobile[7:],
		EncryptMobile: utils.MobileEncrypt(h.Member.MemberMobile),
		AddTime:       int32(time.Now().Unix()),
		OrderAmount:   cast.ToFloat32(h.Product.Price),
		OrderState:    20, // 默认支付，避免通知支付时查询到不是支付单导致失败
		PaymentCode:   "pointpay",
		PaymentTime:   int32(time.Now().Unix()),
		TradeNo:       fmt.Sprintf("%s%d", h.IntegralExchange.OrderSn, h.IntegralExchange.Id),
		// BuyerMsg:           "",
		GoodsId:         h.UpetGoods.GoodsId,
		GoodsName:       html.UnescapeString(h.UpetGoods.GoodsName),
		GoodsPrice:      h.UpetGoods.GoodsPrice,
		GoodsNum:        1,
		GoodsImage:      h.UpetGoods.GoodsImage,
		CommisRate:      200,
		GcId:            h.UpetGoods.GcId,
		VrIndate:        h.UpetGoods.VirtualIndate,
		VrInvalidRefund: h.UpetGoods.VirtualInvalidRefund,
		OrderFrom:       7, // 积分商城
		GoodsSpec:       strings.Join(specs, " "),
	}

	if _, err := h.UpetDb.Insert(vrOrder); err != nil {
		return 0, errors.New("插入商城虚拟订单出错 " + err.Error())
	}

	return vrOrder.OrderId, nil
}

// 针对优惠券类型的发送门店券，出错自动重试
// 不依赖handler是因为这里有很长时间的重试逻辑，后期可能考虑用定时任务调用
func typeCouponRetrySend(ex *models.IntegralExchange, couponId int32) {
	success, _ := typeCouponSend(ex, couponId)
	if success {
		return
	}

	// 异步重试
	go func() {
		// 重试频率
		durations := []time.Duration{
			3 * time.Second,
			5 * time.Minute,
			1 * time.Hour,
			24 * time.Hour,
		}
		for _, duration := range durations {
			time.Sleep(duration)
			if success, _ := typeCouponSend(ex, couponId); success {
				break
			}
		}
	}()
}

// 单次发送门店券，返回请求是否成功，处理是否失败
func typeCouponSend(ex *models.IntegralExchange, couponId int32) (success bool, err error) {
	res, data, err := zilong.CouponSendMulti(&zilong.CouponSendMultiReq{
		Number:        1,
		PhoneArr:      []string{utils.MobileDecrypt(ex.EncryptMobile)},
		TemplateIdArr: []int32{couponId},
	})

	defer func() {
		// 如果子龙返回的数据格式变化，可能会panic
		if e := recover(); e != nil {
			err = fmt.Errorf("panic %s", e)
		}
		if err != nil {
			glog.Infof("积分商城 typeCouponSend 入参：%s，券Id：%d，子龙返回：%s，错误：%s",
				kit.JsonEncode(ex), couponId, kit.JsonEncode(res), err.Error())
		}
	}()

	// 是请求出错，则等待下次重试
	if err != nil {
		return
	}

	// 如果只有日期，则时间变成当天最后一秒
	if len(data[0].PeriodValidityEndTime) <= 10 {
		data[0].PeriodValidityEndTime += " 23:59:59"
	}
	t, err := helpers.StrToTime(data[0].PeriodValidityEndTime)
	if err != nil {
		return true, err
	}

	_, err = models.GetDBConn().Table("dc_order.integral_exchange").Where("id = ?", ex.Id).Update(&models.IntegralExchange{
		CouponCode:    data[0].CouponList[0].CouponCode,
		CouponEndTime: int32(t.Unix()),
		FinnshedTime:  int32(time.Now().Unix()),
	})

	return true, err
}

// 发布消费积分变化mq
func PublishIntegralChange(integral *oc.IntegralNotify) bool {
	glog.Info("积分变化mq参数; content: ", kit.JsonEncode(integral))
	// 错误日志
	orderSn := integral.Ordersn
	if len(orderSn) == 0 {
		orderSn = integral.Oldordersn
	}
	integralJson, err := json.Marshal(&integral)
	if err != nil {
		glog.Error("发布积分变化mq失败,订单号:", orderSn, "-积分类型:", integral.Integraltype, "-err:", err)
	}
	result := utils.PublishRabbitMQ("oc-sz-integral-notify", string(integralJson), "ordercenter")
	glog.Error("integral_center_public_mq-发布结果:", result, "单号：", orderSn)
	return result
}

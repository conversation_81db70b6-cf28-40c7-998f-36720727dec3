package services

import (
	"bytes"
	"context"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"github.com/xuri/excelize/v2"
	"integral-center/helpers"
	"integral-center/helpers/order"
	"integral-center/models"
	"integral-center/proto/et"
	"integral-center/proto/igc"
	"time"
)

type OrderService struct {
}

// List 后台订单列表
func (o *OrderService) List(ctx context.Context, in *igc.OrderListRequest) (out *igc.OrderListResponse, e error) {
	out = &igc.OrderListResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("积分商城 OrderList 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	var ies []*models.IntegralExchangeExtend
	query, err := models.QueryIntegralExchangeExtendByReq(in)
	if err != nil {
		out.Message = err.Error()
		return
	}

	pr := &helpers.PaginateReq{
		Count:    query.Clone().Select("count(*)"),
		List:     query,
		Page:     in.PageIndex,
		PageSize: in.PageSize,
	}
	if err := pr.Paginate(&out.Total, &ies); err != nil {
		out.Message = err.Error()
		return
	}

	if err := models.PreloadExtendGoods(ies); err != nil {
		out.Message = err.Error()
		return
	}
	// 格式化数据
	for _, ie := range ies {
		out.Data = append(out.Data, ie.ToOrderList())
	}

	out.Code = 200
	return
}

// Detail 后台订单详情
func (o *OrderService) Detail(ctx context.Context, in *igc.OrderRequest) (out *igc.OrderDetailResponse, e error) {
	out = &igc.OrderDetailResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("积分商城 OrderDetail 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	db := models.GetDBConn()
	extend := new(models.IntegralExchangeExtend)

	if has, err := db.Table("dc_order.integral_exchange").Alias("e").
		Join("left", "dc_order.integral_exchange_address a", "a.exchange_id = e.id").
		Where("e.delete_time = 0 and e.order_sn = ?", in.OrderSn).Get(extend); err != nil {
		out.Message = "查询订单出错" + err.Error()
		return
	} else if !has {
		out.Message = "订单不存在"
		return
	}

	if err := db.Table("dc_order.integral_exchange_goods").Where("exchange_id = ?", extend.IntegralExchange.Id).
		Find(&extend.IntegralExchangeGoods); err != nil {
		out.Message = "查询商品出错 " + err.Error()
		return
	}

	if detail, err := extend.ToOrderDetail(); err != nil {
		out.Message = err.Error()
		return
	} else {
		out.Order = detail.Order
		out.Coupon = detail.Coupon
		out.VerifyCode = detail.VerifyCode
		out.Steps = detail.Steps
	}

	out.Code = 200
	return
}

// Export 积分订单导出
func (o *OrderService) Export(ctx context.Context, in *igc.OrderListRequest) (out *igc.OrderResponse, e error) {
	out = &igc.OrderResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("积分商城 OrderExport 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	db := models.GetDBConn()
	query, err := models.QueryIntegralExchangeExtendByReq(in)
	if err != nil {
		out.Message = err.Error()
		return
	}

	task := &models.IntegralExchangeTask{
		Type:   2,
		Req:    kit.JsonEncode(in),
		State:  models.IETaskStateIng,
		UserNo: helpers.GetBossUserNoByCtx(ctx),
	}
	if _, err := db.Table("dc_order.integral_exchange_task").Insert(task); err != nil {
		out.Message = "插入任务出错 " + err.Error()
		return
	}

	// 异步处理导出
	go order.Export(task.Id, query)

	out.Code = 200
	return
}

// ExportList 订单导出历史
func (o *OrderService) ExportList(ctx context.Context, in *igc.OrderExportListRequest) (out *igc.OrderExportListResponse, e error) {
	out = &igc.OrderExportListResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("积分商城 OrderExportList 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	db := models.GetDBConn()
	query := db.Table("dc_order.integral_exchange_task").Select("state,result,url,created_at").
		Where("user_no = ? and type = ?", helpers.GetBossUserNoByCtx(ctx), 2).
		OrderBy("id desc")

	pr := &helpers.PaginateReq{
		Count:    query.Clone().Select("count(*)"),
		List:     query,
		Page:     in.PageIndex,
		PageSize: in.PageSize,
	}
	if err := pr.Paginate(&out.Total, &out.Data); err != nil {
		out.Message = err.Error()
		return
	}

	for _, task := range out.Data {
		task.StateText = models.IETaskStateMap[task.State]
	}

	out.Code = 200
	return
}

// ExpressCompanies 快递公司
func (o *OrderService) ExpressCompanies(ctx context.Context, in *igc.OrderEmptyRequest) (out *igc.ExpressCompaniesResponse, e error) {
	out = &igc.ExpressCompaniesResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("积分商城 OrderExpressCompanies 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	upetDb := models.GetUpetDBConn()
	if err := upetDb.Table("upet_express").Select("e_name as name,e_code_kdniao as code").Where("e_state = '1'").
		OrderBy("e_order asc,e_letter asc").Find(&out.Data); err != nil {
		out.Message = "查询出错 " + err.Error()
		return
	}

	out.Code = 200
	return
}

// ExpressStore 发货或者更新物流
func (o *OrderService) ExpressStore(ctx context.Context, in *igc.ExpressStoreRequest) (out *igc.OrderResponse, e error) {
	out = &igc.OrderResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("积分商城 OrderExpressStore 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if len(in.ShippingCode) < 1 || len(in.ShippingEcode) < 1 {
		out.Message = "参数错误"
		return
	}

	db := models.GetDBConn()
	extend := new(models.IntegralExchangeExtend)

	if has, err := db.Table("dc_order.integral_exchange").Alias("e").
		Join("inner", "dc_order.integral_exchange_address a", "a.exchange_id = e.id").
		Where("type = ?", models.IETypeGoods).
		Where("e.order_sn = ?", in.OrderSn).Get(extend); err != nil {
		out.Message = "查询订单出错" + err.Error()
		return
	} else if !has {
		out.Message = "订单不存在"
		return
	}

	if extend.State != models.IEStateDelivery && extend.State != models.IEStateTake {
		out.Message = "订单状态 " + extend.GetStateText() + " 不允许这个操作"
		return
	}

	var shippingEname string
	upetDb := models.GetUpetDBConn()
	if has, err := upetDb.Table("upet_express").Where("e_code_kdniao = ?", in.ShippingEcode).Select("e_name").Get(&shippingEname); err != nil {
		out.Message = "查询物流公司出错 " + err.Error()
		return
	} else if !has {
		out.Message = "物流公司编码不存在"
		return
	}

	session := db.NewSession()
	defer session.Close()
	_ = session.Begin()

	if err := extend.Delivery(session, &models.IntegralExchangeAddress{
		ShippingTime:  int32(time.Now().Unix()),
		ShippingCode:  in.ShippingCode,
		ShippingEcode: in.ShippingEcode,
		ShippingEname: shippingEname,
	}); err != nil {
		_ = session.Rollback()
		out.Message = err.Error()
		return
	}

	if err := session.Commit(); err != nil {
		out.Message = "提交事务出错 " + err.Error()
		return
	}

	out.Code = 200
	return
}

// ExpressImportTemplate 物流导入模板
func (o *OrderService) ExpressImportTemplate(ctx context.Context, in *igc.OrderEmptyRequest) (out *igc.ExpressImportTemplateResponse, e error) {
	out = &igc.ExpressImportTemplateResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("积分商城 ExpressImportTemplate 入参：", kit.JsonEncode(in), "，返回：", out.Code, out.Message)
		}
	}()

	f := excelize.NewFile()

	// 文本样式
	styleId, _ := f.NewStyle(&excelize.Style{
		NumFmt: 49,
	})
	_ = f.SetColStyle("Sheet1", "A", styleId)
	_ = f.SetColStyle("Sheet1", "C", styleId)

	_ = f.SetCellValue("Sheet1", "A1", "单号")
	_ = f.SetCellValue("Sheet1", "B1", "快递公司编码")
	_ = f.SetCellValue("Sheet1", "C1", "快递单号")

	if buffer, err := f.WriteToBuffer(); err != nil {
		out.Message = err.Error()
		return
	} else {
		out.Template = buffer.Bytes()
	}

	out.Code = 200
	return
}

// ExpressImport 批量导入发货
func (o *OrderService) ExpressImport(ctx context.Context, in *igc.ExpressImportRequest) (out *igc.OrderResponse, e error) {
	out = &igc.OrderResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("积分商城 ExpressImport", "，返回：", kit.JsonEncode(out))
		}
	}()

	file, err := excelize.OpenReader(bytes.NewReader(in.File))
	if err != nil {
		out.Message = "读取文件出错 " + err.Error()
		return
	}
	defer file.Close()

	if err := order.ExpressImport(file, helpers.GetBossUserNoByCtx(ctx)); err != nil {
		out.Message = err.Error()
		return
	}

	out.Code = 200
	return
}

// ExpressImportList 批量导入历史
func (o *OrderService) ExpressImportList(ctx context.Context, in *igc.ExpressImportListRequest) (out *igc.ExpressImportListResponse, e error) {
	out = &igc.ExpressImportListResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("积分商城 ExpressImportList 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	db := models.GetDBConn()
	query := db.Table("dc_order.integral_exchange_task").Select("created_at,result,url").
		Where("user_no = ? and type = 1", helpers.GetBossUserNoByCtx(ctx)).
		OrderBy("id desc")

	pr := &helpers.PaginateReq{
		Count:    query.Clone().Select("count(*)"),
		List:     query,
		Page:     in.PageIndex,
		PageSize: in.PageSize,
	}
	if err := pr.Paginate(&out.Total, &out.Data); err != nil {
		out.Message = err.Error()
		return
	}

	out.Code = 200
	return
}

// AWStore 小程序立即兑换
func (o *OrderService) AWStore(ctx context.Context, in *igc.AWOrderStoreRequest) (out *igc.OrderResponse, e error) {
	out = &igc.OrderResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("积分商城 OrderAWStore 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if len(in.ScrmId) == 0 {
		out.Message = "用户信息不存在"
		return
	}
	if in.GoodsId < 1 {
		out.Message = "商品信息不能为空"
		return
	}

	if err := order.Store(in); err != nil {
		out.Message = err.Error()
		return
	}

	out.Code = 200
	return
}

// AWList 小程序订单列表
func (o *OrderService) AWList(ctx context.Context, in *igc.AWOrderListRequest) (out *igc.AWOrderListResponse, e error) {
	out = &igc.AWOrderListResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("积分商城 OrderAWList 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if len(in.ScrmId) == 0 {
		out.Message = "用户信息不存在"
		return
	}

	exchangeType := cast.ToInt32(in.Type)
	if exchangeType < 1 || exchangeType > 3 {
		out.Message = "订单类型参数错误"
		return
	}

	db := models.GetDBConn()
	var ies []*models.IntegralExchangeExtend

	query := db.Table("dc_order.integral_exchange").Alias("e").
		Join("left", "dc_order.integral_exchange_address a", "a.exchange_id = e.id").
		Where("e.delete_time = 0").
		Where("e.scrm_id = ? and type = ?", in.ScrmId, exchangeType).
		OrderBy("e.id desc")

	pr := &helpers.PaginateReq{
		Count:    query.Clone().Select("count(*)"),
		List:     query,
		Page:     in.PageIndex,
		PageSize: in.PageSize,
	}
	if err := pr.Paginate(&out.Total, &ies); err != nil {
		out.Message = err.Error()
		return
	}

	if err := models.PreloadExtendGoods(ies); err != nil {
		out.Message = err.Error()
		return
	}
	// 格式化数据
	for _, ie := range ies {
		out.Data = append(out.Data, ie.ToOrderList())
	}

	out.Code = 200
	return
}

// AWDetail 小程序订单详情
func (o *OrderService) AWDetail(ctx context.Context, in *igc.AWOrderRequest) (out *igc.AWOrderDetailResponse, e error) {
	out = &igc.AWOrderDetailResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("积分商城 OrderAWDetail 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if len(in.ScrmId) == 0 {
		out.Message = "用户信息不存在"
		return
	}
	if len(in.OrderSn) == 0 {
		out.Message = "订单号不能为空"
		return
	}

	db := models.GetDBConn()
	extend := new(models.IntegralExchangeExtend)

	if has, err := db.Table("dc_order.integral_exchange").Alias("e").
		Join("left", "dc_order.integral_exchange_address a", "a.exchange_id = e.id").
		Where("e.delete_time = 0").
		Where("e.order_sn = ? and e.scrm_id = ?", in.OrderSn, in.ScrmId).Get(extend); err != nil {
		out.Message = "查询订单出错" + err.Error()
		return
	} else if !has {
		out.Message = "订单不存在"
		return
	}

	if err := db.Table("dc_order.integral_exchange_goods").Where("exchange_id = ?", extend.IntegralExchange.Id).
		Find(&extend.IntegralExchangeGoods); err != nil {
		out.Message = "查询商品出错 " + err.Error()
		return
	}

	if detail, err := extend.ToOrderDetail(); err != nil {
		out.Message = err.Error()
		return
	} else {
		out.Data = detail.Order
		out.Coupon = detail.Coupon
		out.VerifyCode = detail.VerifyCode
	}

	out.Code = 200
	return
}

// AWConfirmReceipt 确认收货
func (o *OrderService) AWConfirmReceipt(ctx context.Context, in *igc.AWOrderRequest) (out *igc.OrderResponse, e error) {
	out = &igc.OrderResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("积分商城 OrderAWConfirmReceipt 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if in.OrderSn == "" || in.ScrmId == "" {
		out.Message = "参数错误"
		return
	}

	db := models.GetDBConn()
	orderInfo := &models.IntegralExchange{}
	if has, err := db.Table("dc_order.integral_exchange").Where("order_sn=? and scrm_id=? and type=1", in.OrderSn, in.ScrmId).
		Get(orderInfo); err != nil {
		out.Message = err.Error()
		return
	} else if !has {
		out.Message = "订单信息不存在"
		return
	}

	if orderInfo.State != models.IEStateTake {
		out.Message = "订单不是已发货状态"
		return
	}

	if _, err := db.Table("dc_order.integral_exchange").Where("id=?", orderInfo.Id).Update(&models.IntegralExchange{
		FinnshedTime: int32(time.Now().Unix()),
		State:        models.IEStateFinished,
	}); err != nil {
		out.Message = "订单收货失败"
		return
	}

	out.Code = 200
	return
}

// AWDeliveryDetail 物流详情
func (o *OrderService) AWDeliveryDetail(ctx context.Context, in *igc.AWOrderRequest) (out *igc.AWDeliveryDetailResponse, e error) {
	out = &igc.AWDeliveryDetailResponse{Code: 400}

	defer func() {
		if out.Code == 400 {
			glog.Info("积分商城 OrderAWDeliveryDetail 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	if in.OrderSn == "" || in.ScrmId == "" {
		out.Message = "参数错误"
		return
	}

	db := models.GetDBConn()
	orderInfo := &models.IntegralExchange{}

	if has, err := db.Table("dc_order.integral_exchange").Where("order_sn=? and scrm_id=? and type=1", in.OrderSn, in.ScrmId).Get(orderInfo); err != nil {
		out.Message = err.Error()
		return
	} else if !has {
		out.Message = "订单不存在"
		return
	}

	if orderInfo.State == models.IEStateTake || orderInfo.State == models.IEStateFinished {
		addressInfo := &models.IntegralExchangeAddress{}
		if has, err := db.Table("dc_order.integral_exchange_address").Where("exchange_id=?", orderInfo.Id).Get(addressInfo); err != nil {
			out.Message = err.Error()
			return
		} else if !has {
			out.Message = "未找到单号信息"
			return
		}

		express := &models.UpetExpress{}
		has, err := models.GetUpetDBConn().Where("e_code_kdniao=?", addressInfo.ShippingEcode).Get(express)
		if !has || err != nil {
			out.Message = "快递公司不存在"
			return
		}

		etClient := et.GetExternalClient()
		rs, err := etClient.Delivery.AliInfo(ctx, &et.DeliveryAliInfoRequest{
			Number: addressInfo.ShippingCode,
			Type:   addressInfo.ShippingEcode,
			Mobile: addressInfo.Mobile,
		})

		// 以下任何时候都返回200
		out.Code = 200
		out.ShippingCode = addressInfo.ShippingCode
		out.ExpressName = express.EName
		if err != nil {
			out.Message = "物流查询失败"
			return
		}
		out.Status = rs.Status
		out.Msg = rs.Msg
		out.Result = &igc.AWDeliveryDetailResponse_Result{
			Type:    rs.Type,
			Number:  rs.Number,
			ExpName: rs.ExpName,
		}
		for _, v := range rs.List {
			out.Result.List = append(out.Result.List, &igc.AWDeliveryDetailResponse_Node{
				Time:   v.Time,
				Status: v.Desc,
			})
		}
	} else {
		out.Message = "订单不是已发货状态"
	}
	return
}

func (o *OrderService) IntegralChangeMessage(ctx context.Context, request *igc.IntegralChangeMessageRequest) (out *igc.OrderResponse, e error) {
	out = &igc.OrderResponse{Code: 400}
	err := sendIntegralChangeMsg(request.MemberId, cast.ToInt(request.IntegralType), cast.ToInt(request.InsertIntegral), cast.ToInt(request.OrgId))
	if err != nil {
		return out, err
	}
	out.Code = 200
	return out, nil
}

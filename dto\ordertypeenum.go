package dto

//订单类型枚举
type OrderTypeEnum int32

const (
	AllOrder           OrderTypeEnum = 0 //全部
	ConsultingOrder    OrderTypeEnum = 1 //咨询订单
	PhysicalStoreOrder OrderTypeEnum = 2 //门店订单
	BookingOrder       OrderTypeEnum = 3 //预约订单
	ECommerceOrder     OrderTypeEnum = 4 //电商订单
	IntegralOrder      OrderTypeEnum = 5 //积分订单
	GroupOrder         OrderTypeEnum = 6 //拼团订单
)

func (ordertype OrderTypeEnum) String() string {
	switch ordertype {
	case AllOrder:
		return "全部"
	case ConsultingOrder:
		return "咨询订单"
	case PhysicalStoreOrder:
		return "门店订单"
	case BookingOrder:
		return "预约订单"
	case ECommerceOrder:
		return "电商订单"
	case IntegralOrder:
		return "积分订单"
	case GroupOrder:
		return "拼团订单"
	default:
		return "UNKNOWN"
	}
}
